-- =====================================================
-- 为回收订单表添加快递状态字段
-- =====================================================
-- 执行时间：请在数据库维护时间执行
-- 执行前请备份数据库
--
-- 快递状态说明：
-- 0 = 暂无轨迹（默认状态）
-- 1 = 已揽收（快递公司已收件）
-- 2 = 运输中（快递在途中）
-- 3 = 已签收（快递已送达）
-- 4 = 异常（快递出现问题）
-- =====================================================

-- 1. 添加快递状态字段
ALTER TABLE `www_yz_she_recycle_orders`
ADD COLUMN `express_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '快递状态:0=暂无轨迹,1=已揽收,2=运输中,3=已签收,4=异常'
AFTER `express_fee`;

-- 2. 添加快递状态索引（提高查询性能）
ALTER TABLE `www_yz_she_recycle_orders`
ADD INDEX `idx_express_status`(`express_status`) USING BTREE;

-- 3. 更新现有数据的快递状态
-- 如果已有快递单号的订单，设置为已揽收状态
UPDATE `www_yz_she_recycle_orders`
SET `express_status` = 1
WHERE `express_number` IS NOT NULL
  AND `express_number` != ''
  AND `express_status` = 0;

-- 4. 验证字段添加是否成功
SELECT
    `id`,
    `order_no`,
    `express_company`,
    `express_number`,
    `express_status`,
    `status`
FROM `www_yz_she_recycle_orders`
WHERE `express_number` IS NOT NULL
LIMIT 5;

-- 5. 查看表结构确认字段已添加
-- DESCRIBE `www_yz_she_recycle_orders`;

-- =====================================================
-- 执行完成后，请重启PHP服务以清除缓存
-- =====================================================
