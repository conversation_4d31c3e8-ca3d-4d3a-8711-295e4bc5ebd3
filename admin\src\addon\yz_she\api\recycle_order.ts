import request from '@/utils/request'

/**
 * 获取回收订单列表
 */
export function getRecycleOrderList(params: Record<string, any>) {
    return request.get('yz_she/recycle_order/lists', { params })
}

/**
 * 获取回收订单详情
 */
export function getRecycleOrderInfo(id: number) {
    return request.get(`yz_she/recycle_order/info/${id}`)
}

/**
 * 获取状态统计数据
 */
export function getRecycleOrderStatusCounts() {
    return request.get('yz_she/recycle_order/status_counts')
}

/**
 * 获取状态选项
 */
export function getRecycleOrderStatusOptions() {
    return request.get('yz_she/recycle_order/get_status_options')
}

/**
 * 获取来源类型选项
 */
export function getRecycleOrderSourceTypeOptions() {
    return request.get('yz_she/recycle_order/get_source_type_options')
}

/**
 * 获取配送方式选项
 */
export function getRecycleOrderDeliveryTypeOptions() {
    return request.get('yz_she/recycle_order/get_delivery_type_options')
}

/**
 * 收货确认
 */
export function receiveRecycleOrder(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/receive/${id}`, data)
}

/**
 * 开始质检
 */
export function startQualityCheck(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/start_quality/${id}`, data)
}

/**
 * 完成质检
 */
export function completeQualityCheck(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/complete_quality/${id}`, data)
}

/**
 * 订单结算
 */
export function settlementRecycleOrder(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/settlement/${id}`, data)
}

/**
 * 退回订单
 */
export function returnRecycleOrder(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/return/${id}`, data)
}

/**
 * 更新快递信息
 */
export function updateExpressInfo(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/update_express/${id}`, data)
}

/**
 * 更新订单备注
 */
export function updateOrderNotes(id: number, data: Record<string, any>) {
    return request.post(`yz_she/recycle_order/update_notes/${id}`, data)
}

/**
 * 获取订单日志
 */
export function getOrderLogs(id: number) {
    return request.get(`yz_she/recycle_order/logs/${id}`)
}

/**
 * 批量操作
 */
export function batchRecycleOrder(data: Record<string, any>) {
    return request.post('yz_she/recycle_order/batch', data)
}

/**
 * 导出订单数据
 */
export function exportRecycleOrder(params: Record<string, any>) {
    return request.get('yz_she/recycle_order/export', { params })
}