<template>
    <div class="main-container">
        <el-card class="box-card !border-none" shadow="never">


            <!-- 搜索条件 -->
            <el-card class="box-card !border-none mb-[10px]" shadow="never">
                <el-form :model="quoteOrderTable.searchParam" label-width="90px" :inline="true">
                    <el-form-item label="订单号" prop="order_no">
                        <el-input v-model="quoteOrderTable.searchParam.order_no" placeholder="请输入订单号" class="w-[240px]" />
                    </el-form-item>
                    <el-form-item label="订单状态" prop="status">
                        <el-select v-model="quoteOrderTable.searchParam.status" placeholder="请选择状态" class="w-[240px]" clearable>
                            <el-option label="全部" value="" />
                            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间" prop="create_time">
                        <el-date-picker v-model="quoteOrderTable.searchParam.create_time" type="datetimerange"
                            value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="loadQuoteOrderList()">搜索</el-button>
                        <el-button @click="resetForm">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 操作栏 -->
            <div class="flex justify-between items-center mb-[10px]">
                <div class="flex items-center">
                    <el-button type="danger" :disabled="!quoteOrderTable.selectData.length" @click="batchCancel">
                        批量取消
                    </el-button>

                </div>
                <div class="flex items-center">
                    <el-button circle @click="loadQuoteOrderList">
                        <template #icon>
                            <RefreshRight />
                        </template>
                    </el-button>
                </div>
            </div>

            <!-- 数据表格 -->
            <el-table :data="quoteOrderTable.data" size="large" v-loading="quoteOrderTable.loading" ref="quoteOrderTableRef"
                @selection-change="handleSelectionChange" :row-class-name="getRowClassName">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="order_no" label="订单号" min-width="140" show-overflow-tooltip />
                <el-table-column label="商品信息" min-width="280">
                    <template #default="{ row }">
                        <div class="flex items-start space-x-3">
                            <!-- 品牌LOGO（替代商品图片） -->
                            <div class="flex-shrink-0">
                                <el-image 
                                    class="w-[60px] h-[60px] rounded-lg border" 
                                    :src="img(row.brand?.logo)" 
                                    fit="cover"
                                    :preview-src-list="row.brand?.logo ? [img(row.brand.logo)] : []"
                                    :title="row.brand?.name || '品牌logo'"
                                >
                                    <template #error>
                                        <div class="w-[60px] h-[60px] flex items-center justify-center bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                                            <div class="text-center">
                                                <div class="text-xs text-gray-400 font-bold">
                                                    {{ row.brand?.name?.charAt(0) || 'B' }}
                                                </div>
                                                <div class="text-xs text-gray-400 mt-1">LOGO</div>
                                            </div>
                                        </div>
                                    </template>
                                </el-image>
                            </div>
                            
                            <!-- 商品信息 -->
                            <div class="flex-1 min-w-0">
                                <!-- 商品名称 -->
                                <div class="font-medium text-sm mb-1 line-clamp-2" :title="row.product_name">
                                    {{ row.product_name }}
                                </div>
                                
                                
                                <!-- 商品编码/货号 -->
                                <div class="text-xs text-gray-500 mb-1" v-if="row.product_code">
                                    <span class="inline-flex items-center">
                                        <el-icon class="mr-1"><Goods /></el-icon>
                                        {{ row.product_code }}
                                    </span>
                                </div>
                                
                                <!-- 用户备注提示 -->
                                <div class="mt-1" v-if="row.user_note">
                                    <el-tag size="small" type="warning" effect="plain">
                                        <el-icon class="mr-1"><ChatDotRound /></el-icon>
                                        有备注
                                    </el-tag>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="用户信息" min-width="120">
                    <template #default="{ row }">
                        <div v-if="row.user">
                            <div class="text-sm">{{ row.user.nickname || row.user.username }}</div>
                            <div class="text-xs text-gray-500">{{ row.user.mobile }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="分类/品牌" min-width="120">
                    <template #default="{ row }">
                        <div>
                            <div class="text-sm">{{ row.category?.name }}</div>
                            <div class="text-xs text-gray-500">{{ row.brand?.name }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="status_text" label="状态" width="100">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" :class="getStatusClass(row.status)">
                            {{ row.status_text }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="quote_price_text" label="估价金额" width="100" />
                <el-table-column label="估价员" width="100">
                    <template #default="{ row }">
                        <span v-if="row.admin">{{ row.admin.real_name || row.admin.username }}</span>
                        <span v-else class="text-gray-400">未分配</span>
                    </template>
                </el-table-column>
                <el-table-column prop="create_time" label="创建时间" width="160" />
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="showDetail(row.id)">详情</el-button>
                        <el-button v-if="row.status === 1" type="success" link @click="showQuoteDialog(row)">估价</el-button>
                        <el-button v-if="[1, 2].includes(row.status)" type="danger" link @click="cancelOrder(row.id)">取消</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="flex justify-end mt-[16px]">
                <el-pagination v-model:current-page="quoteOrderTable.page" v-model:page-size="quoteOrderTable.limit"
                    layout="total, sizes, prev, pager, next, jumper" :total="quoteOrderTable.total"
                    @size-change="loadQuoteOrderList()" @current-change="loadQuoteOrderList" />
            </div>
        </el-card>

        <!-- 估价对话框 -->
        <el-dialog v-model="quoteDialog.visible" title="商品估价" width="700px" :close-on-click-modal="false">
            <el-form :model="quoteDialog.form" :rules="quoteDialog.rules" label-width="100px" ref="quoteFormRef">
                <el-form-item label="估价金额" prop="quote_price">
                    <div class="flex items-center">
                        <el-input-number 
                            v-model="quoteDialog.form.quote_price" 
                            :min="0" 
                            :precision="2" 
                            placeholder="请输入估价金额"
                            class="w-[200px]" 
                        />
                        <span class="ml-2 text-gray-500">元</span>
                    </div>
                </el-form-item>
                
                <el-form-item label="成色评分" prop="condition_score">
                    <div class="w-full">
                        <!-- 评分滑块和输入框 -->
                        <div class="flex items-center space-x-4 mb-3">
                            <el-slider 
                                v-model="quoteDialog.form.condition_score" 
                                :min="1" 
                                :max="10" 
                                :step="0.5"
                                show-stops
                                class="flex-1 max-w-[400px]"
                                @input="onConditionScoreChange"
                            />
                            <el-input-number
                                v-model="quoteDialog.form.condition_score"
                                :min="1"
                                :max="10"
                                :step="0.5"
                                :precision="1"
                                class="w-[120px]"
                                @change="onConditionScoreChange"
                            />
                        </div>
                        
                        <!-- 成色显示 -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="text-lg font-bold text-blue-600">
                                    {{ Math.floor(quoteDialog.form.condition_score) }}成新
                                </div>
                                <div class="text-sm text-gray-500">
                                    ({{ quoteDialog.form.condition_score }}分)
                                </div>
                            </div>
                            <div class="text-sm font-medium text-green-600">
                                {{ getConditionText(quoteDialog.form.condition_score) }}
                            </div>
                        </div>
                        
                        
                    </div>
                </el-form-item>
                
                <el-form-item label="估价说明" prop="quote_note">
                    <el-input 
                        v-model="quoteDialog.form.quote_note" 
                        type="textarea" 
                        :rows="3" 
                        placeholder="请详细说明估价依据，如：成色状况、配件完整性、市场行情等"
                        maxlength="500"
                        show-word-limit
                    />
                </el-form-item>
                
                <el-form-item label="管理员备注" prop="admin_note">
                    <el-input 
                        v-model="quoteDialog.form.admin_note" 
                        type="textarea" 
                        :rows="2" 
                        placeholder="内部备注信息（用户不可见）"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="quoteDialog.visible = false">取消</el-button>
                <el-button type="primary" @click="submitQuote" :loading="quoteDialog.loading">确定估价</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RefreshRight, Picture, Goods, ChatDotRound, Star } from '@element-plus/icons-vue'
import { img } from '@/utils/common'
import { useRouter } from 'vue-router'
import useUserStore from '@/stores/modules/user'
import {
    quoteOrderList,
    quoteOrderQuote,
    quoteOrderCancel,
    quoteOrderStatusOptions,
    quoteOrderBatch
} from '@/addon/yz_she/api/quote_order'

const router = useRouter()
const userStore = useUserStore()

// 表格数据
const quoteOrderTable = reactive({
    page: 1,
    limit: 10,
    total: 0,
    loading: true,
    data: [],
    searchParam: {
        order_no: '',
        status: '',
        create_time: []
    },
    selectData: []
})

// 状态选项
const statusOptions = ref([])

// 估价对话框
// 估价对话框
const quoteDialog = reactive({
    visible: false,
    loading: false,
    orderId: 0,
    form: {
        quote_price: 0,
        condition_score: 8.0,
        quote_note: '',
        admin_note: ''
    },
    rules: {
        quote_price: [
            { required: true, message: '请输入估价金额', trigger: 'blur' },
            { type: 'number', min: 0.01, message: '估价金额必须大于0', trigger: 'blur' }
        ],
        condition_score: [
            { required: true, message: '请设置成色评分', trigger: 'blur' },
            { type: 'number', min: 1, max: 10, message: '成色评分范围为1-10分', trigger: 'blur' }
        ]
    }
})

const quoteOrderTableRef = ref()
const quoteFormRef = ref()

/**
 * 获取估价订单列表
 */
const loadQuoteOrderList = (page: number = 1) => {
    quoteOrderTable.loading = true
    quoteOrderTable.page = page

    quoteOrderList({
        page: quoteOrderTable.page,
        limit: quoteOrderTable.limit,
        ...quoteOrderTable.searchParam
    }).then(res => {
        quoteOrderTable.loading = false
        quoteOrderTable.data = res.data.data
        quoteOrderTable.total = res.data.total
    }).catch(() => {
        quoteOrderTable.loading = false
    })
}

/**
 * 获取状态选项
 */
const loadStatusOptions = () => {
    quoteOrderStatusOptions().then(res => {
        // 转换后端返回的数据格式
        const options = []
        for (const [key, value] of Object.entries(res.data)) {
            options.push({
                value: parseInt(key),
                label: value.name
            })
        }
        statusOptions.value = options
    }).catch(() => {
        // 状态选项加载失败时使用默认值
        statusOptions.value = [
            { value: 1, label: '估价中' },
            { value: 2, label: '待确认' },
            { value: 3, label: '待发货' },
            { value: 4, label: '已完成' },
            { value: 5, label: '已取消' }
        ]
    })
}

/**
 * 重置搜索表单
 */
const resetForm = () => {
    quoteOrderTable.searchParam = {
        order_no: '',
        status: '',
        create_time: []
    }
    loadQuoteOrderList()
}

/**
 * 获取行样式类名 - 为不同状态设置背景色
 */
const getRowClassName = ({ row }: { row: any }) => {
    if (row.status === 5) {
        return 'cancelled-row'  // 已取消
    }
    if (row.status === 2) {
        return 'pending-confirm-row'  // 待确认
    }
    if (row.status === 1) {
        return 'quoting-row'  // 估价中
    }
    if (row.status === 3) {
        return 'pending-ship-row'  // 待发货
    }
    if (row.status === 4) {
        return 'completed-row'  // 已完成
    }
    return ''
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: number) => {
    const typeMap = {
        1: 'warning',  // 估价中
        2: 'primary',  // 待确认
        3: 'danger',   // 待发货
        4: 'success',  // 已完成
        5: 'info'      // 已取消
    }
    return typeMap[status] || 'info'
}

/**
 * 获取状态标签样式类
 */
const getStatusClass = (status: number) => {
    const classMap = {
        1: 'status-quoting',      // 估价中
        2: 'status-pending',      // 待确认
        3: 'status-shipping',     // 待发货
        4: 'status-completed',    // 已完成
        5: 'status-cancelled'     // 已取消
    }
    return classMap[status] || ''
}

/**
 * 获取成色文本
 */
const getConditionText = (score: number) => {
    if (score >= 10) return '全新'
    if (score >= 9.5) return '九五新'
    if (score >= 9) return '九成新'
    if (score >= 8.5) return '八五新'
    if (score >= 8) return '八成新'
    if (score >= 7.5) return '七五新'
    if (score >= 7) return '七成新'
    if (score >= 6.5) return '六五新'
    if (score >= 6) return '六成新'
    if (score >= 5.5) return '五五新'
    if (score >= 5) return '五成新'
    return '五成新以下'
}

/**
 * 成色评分变化处理
 */
const onConditionScoreChange = (value: number) => {
    // 确保值在有效范围内
    if (value < 1) {
        quoteDialog.form.condition_score = 1
    } else if (value > 10) {
        quoteDialog.form.condition_score = 10
    }
}

/**
 * 表格选择变化
 */
const handleSelectionChange = (selection: any[]) => {
    quoteOrderTable.selectData = selection
}

/**
 * 查看详情 - 跳转到独立详情页面
 */
const showDetail = (id: number) => {
    router.push(`/yz_she/quote_order/detail/${id}`)
}

/**
 * 显示估价对话框
 */
const showQuoteDialog = (row: any) => {
    quoteDialog.visible = true
    quoteDialog.orderId = row.id
    quoteDialog.form = {
        quote_price: 0,
        condition_score: 8.0,
        quote_note: '',
        admin_note: ''
    }
}

/**
 * 提交估价
 */
const submitQuote = () => {
    quoteFormRef.value.validate((valid: boolean) => {
        if (valid) {
            quoteDialog.loading = true
            
            // 获取当前管理员ID
            const adminId = userStore.userInfo?.uid || userStore.userInfo?.id || 0
            
            // 添加admin_id到请求参数
            const quoteData = {
                ...quoteDialog.form,
                admin_id: adminId
            }
            
            quoteOrderQuote(quoteDialog.orderId, quoteData).then(() => {
                quoteDialog.loading = false
                quoteDialog.visible = false
                ElMessage.success('估价成功')
                loadQuoteOrderList()
            }).catch(() => {
                quoteDialog.loading = false
            })
        }
    })
}

/**
 * 取消订单
 */
const cancelOrder = (id: number) => {
    ElMessageBox.prompt('请输入取消原因', '取消订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入取消原因'
    }).then(({ value }) => {
        quoteOrderCancel(id, { cancel_reason: value || '管理员取消' }).then(() => {
            ElMessage.success('取消成功')
            loadQuoteOrderList()
        }).catch(() => {
            ElMessage.error('取消失败')
        })
    }).catch(() => {
        // 用户取消操作
    })
}

/**
 * 批量取消
 */
const batchCancel = () => {
    if (!quoteOrderTable.selectData.length) {
        ElMessage.warning('请选择要取消的订单')
        return
    }

    ElMessageBox.confirm('确定要批量取消选中的订单吗？', '批量取消', {
        type: 'warning'
    }).then(() => {
        const ids = quoteOrderTable.selectData.map(item => item.id)
        quoteOrderBatch({ ids, action: 'cancel' }).then((res) => {
            ElMessage.success(res.message || '批量取消成功')
            loadQuoteOrderList()
            quoteOrderTableRef.value.clearSelection()
        }).catch(() => {
            ElMessage.error('批量取消失败')
        })
    }).catch(() => {
        // 用户取消操作
    })
}

onMounted(() => {
    loadQuoteOrderList()
    loadStatusOptions()
})
</script>

<style lang="scss" scoped>
.main-container {
    min-height: 100%;
    .el-card {
        border: none;
        .el-card__body {
            padding: 20px !important;
        }
    }
}


// 状态标签样式
:deep(.status-quoting) {
    background-color: #fbbf24 !important;
    border-color: #f59e0b !important;
    color: #ffffff !important;
}

:deep(.status-pending) {
    background-color: #3b82f6 !important;
    border-color: #2563eb !important;
    color: #ffffff !important;
}

:deep(.status-shipping) {
    background-color: #ef4444 !important;
    border-color: #dc2626 !important;
    color: #ffffff !important;
}

:deep(.status-completed) {
    background-color: #10b981 !important;
    border-color: #059669 !important;
    color: #ffffff !important;
}

:deep(.status-cancelled) {
    background-color: #6b7280 !important;
    border-color: #4b5563 !important;
    color: #ffffff !important;
}
</style>
