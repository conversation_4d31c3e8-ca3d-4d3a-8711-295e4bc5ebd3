<template>
    <div class="main-container">
        <!-- 页面头部 -->
        <el-card class="box-card !border-none" shadow="never">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <span class="text-page-title">回收订单详情</span>
                    <el-tag v-if="orderInfo.id" :type="getSourceTypeTagType(orderInfo.source_type)" size="large">
                        {{ orderInfo.source_type_text }}
                    </el-tag>
                </div>
                <div class="flex items-center space-x-2">
                    <el-button @click="refreshDetail()">
                        <el-icon><Refresh /></el-icon>
                        刷新
                    </el-button>
                    <el-button @click="goBack()">返回</el-button>
                </div>
            </div>
        </el-card>

        <div v-loading="loading">
            <div v-if="orderInfo.id">
                <!-- 订单概览卡片 -->
                <el-card class="order-overview-card mt-4" shadow="never">
                    <div class="order-overview">
                        <div class="order-basic-info">
                            <div class="order-number">
                                <h2>{{ orderInfo.order_no }}</h2>
                                <el-button type="primary" link @click="copyText(orderInfo.order_no)">
                                    <el-icon><CopyDocument /></el-icon>
                                </el-button>
                            </div>
                            <div class="order-meta">
                                <span class="create-time">{{ orderInfo.create_time_text }}</span>
                                <span class="divider">|</span>
                                <span class="delivery-type">{{ orderInfo.delivery_type_text }}</span>
                            </div>
                        </div>

                        <div class="order-status-info">
                            <el-tag :type="getStatusTagType(orderInfo.status)" size="large" class="status-tag">
                                {{ orderInfo.status_text }}
                            </el-tag>
                            <div class="settlement-status">
                                <el-tag v-if="orderInfo.settlement_status == 1" type="success" size="small">已结算</el-tag>
                                <el-tag v-else type="info" size="small">未结算</el-tag>
                            </div>
                        </div>
                    </div>
                </el-card>

                <!-- 快速操作栏 -->
                <el-card class="quick-actions-card mt-4" shadow="never" v-if="hasQuickActions">
                    <div class="quick-actions">
                        <el-button v-if="orderInfo.status == 2" type="primary" @click="receiveOrder()">
                            <el-icon><Box /></el-icon>
                            收货确认
                        </el-button>
                        <el-button v-if="orderInfo.status == 3" type="warning" @click="startQuality()">
                            <el-icon><Search /></el-icon>
                            开始质检
                        </el-button>
                        <el-button v-if="orderInfo.status == 4" type="success" @click="completeQuality()">
                            <el-icon><Check /></el-icon>
                            完成质检
                        </el-button>
                        <el-button v-if="orderInfo.status == 7 && orderInfo.settlement_status == 0" type="success" @click="settlement()">
                            <el-icon><Money /></el-icon>
                            结算订单
                        </el-button>
                        <el-button v-if="orderInfo.status == 5" type="danger" @click="returnOrder()">
                            <el-icon><RefreshLeft /></el-icon>
                            确认退回
                        </el-button>
                    </div>
                </el-card>

                <!-- 主要信息区域 -->
                <el-row :gutter="20" class="mt-4">
                    <!-- 左侧：商品和用户信息 -->
                    <el-col :span="16">
                        <!-- 商品信息卡片 -->
                        <el-card class="product-info-card mb-4" shadow="never">
                            <template #header>
                                <div class="card-header">
                                    <div class="flex items-center space-x-2">
                                        <el-icon :class="getSourceTypeIconClass(orderInfo.source_type)">
                                            <component :is="getSourceTypeIcon(orderInfo.source_type)" />
                                        </el-icon>
                                        <span>{{ getSourceTypeTitle(orderInfo.source_type) }}</span>
                                    </div>
                                    <el-button v-if="orderInfo.quote_order_id" type="primary" link size="small" @click="viewQuoteOrder(orderInfo.quote_order_id)">
                                        查看估价详情
                                    </el-button>
                                    <el-button v-else-if="orderInfo.product_id" type="primary" link size="small" @click="viewProductDetail(orderInfo.product_id)">
                                        查看商品详情
                                    </el-button>
                                </div>
                            </template>

                            <div class="product-info">
                                <div class="product-image">
                                    <el-image
                                        :src="getProductImage()"
                                        class="product-img"
                                        fit="cover"
                                    >
                                        <template #error>
                                            <div class="image-placeholder">
                                                <el-icon :class="getSourceTypeIconClass(orderInfo.source_type)">
                                                    <component :is="getSourceTypeIcon(orderInfo.source_type)" />
                                                </el-icon>
                                            </div>
                                        </template>
                                    </el-image>
                                </div>

                                <div class="product-details">
                                    <h3 class="product-name">{{ orderInfo.product_name || '待确认商品' }}</h3>
                                    <div class="product-meta">
                                        <div class="meta-item">
                                            <span class="label">分类：</span>
                                            <span class="value">{{ orderInfo.category?.name || '-' }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="label">品牌：</span>
                                            <span class="value">{{ orderInfo.brand?.name || '-' }}</span>
                                        </div>
                                        <div class="meta-item" v-if="orderInfo.product_code">
                                            <span class="label">编码：</span>
                                            <span class="value">{{ orderInfo.product_code }}</span>
                                        </div>
                                        <div class="meta-item">
                                            <span class="label">数量：</span>
                                            <span class="value">{{ orderInfo.quantity }}件</span>
                                        </div>
                                    </div>

                                    <!-- 特殊说明 -->
                                    <div class="special-note" v-if="orderInfo.source_type == 3">
                                        <el-alert
                                            title="批量回收订单，具体商品信息需现场确认"
                                            type="warning"
                                            :closable="false"
                                            show-icon
                                        />
                                    </div>
                                </div>
                            </div>
                        </el-card>

                        <!-- 用户信息卡片 -->
                        <el-card class="user-info-card mb-4" shadow="never">
                            <template #header>
                                <div class="flex items-center space-x-2">
                                    <el-icon class="text-blue-500"><User /></el-icon>
                                    <span>用户信息</span>
                                </div>
                            </template>

                            <div class="user-info">
                                <el-avatar :src="orderInfo.member?.headimg" :size="50" class="user-avatar">
                                    {{ orderInfo.member?.nickname?.charAt(0) || 'U' }}
                                </el-avatar>
                                <div class="user-details">
                                    <h4 class="user-name">{{ orderInfo.member?.nickname || '-' }}</h4>
                                    <div class="user-meta">
                                        <div class="meta-item">
                                            <span class="label">手机：</span>
                                            <span class="value">{{ orderInfo.member?.mobile || '-' }}</span>
                                            <el-button type="primary" link size="small" @click="copyText(orderInfo.member?.mobile)" v-if="orderInfo.member?.mobile">
                                                复制
                                            </el-button>
                                        </div>
                                        <div class="meta-item">
                                            <span class="label">等级：</span>
                                            <span class="value">{{ getMemberLevelText(orderInfo.member?.member_level) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 右侧：价格和状态信息 -->
                    <el-col :span="8">
                        <!-- 价格信息卡片 -->
                        <el-card class="price-info-card mb-4" shadow="never">
                            <template #header>
                                <div class="flex items-center space-x-2">
                                    <el-icon class="text-green-500"><Money /></el-icon>
                                    <span>价格信息</span>
                                </div>
                            </template>

                            <div class="price-info">
                                <div class="price-item">
                                    <div class="price-label">预期价格</div>
                                    <div class="price-value primary">¥{{ orderInfo.expected_price }}</div>
                                </div>

                                <div class="price-item" v-if="orderInfo.voucher_amount > 0">
                                    <div class="price-label">加价券</div>
                                    <div class="price-value warning">+¥{{ orderInfo.voucher_amount }}</div>
                                </div>

                                <div class="price-item" v-if="orderInfo.final_price">
                                    <div class="price-label">最终价格</div>
                                    <div class="price-value success">¥{{ orderInfo.final_price }}</div>
                                </div>

                                <div class="price-item total" v-if="orderInfo.total_amount">
                                    <div class="price-label">结算金额</div>
                                    <div class="price-value danger">¥{{ orderInfo.total_amount }}</div>
                                </div>

                                <div class="price-note" v-if="!orderInfo.final_price">
                                    <el-alert
                                        title="最终价格将在质检完成后确定"
                                        type="info"
                                        :closable="false"
                                        show-icon
                                    />
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 价格信息 -->
                <el-card class="mb-4" header="价格信息">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">¥{{ orderInfo.expected_price }}</div>
                                <div class="text-gray-500">预期价格</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-warning">+¥{{ orderInfo.voucher_amount || 0 }}</div>
                                <div class="text-gray-500">加价券</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-success">¥{{ orderInfo.final_price || '-' }}</div>
                                <div class="text-gray-500">最终价格</div>
                            </div>
                        </el-col>
                        <el-col :span="6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-danger">¥{{ orderInfo.total_amount || '-' }}</div>
                                <div class="text-gray-500">结算金额</div>
                            </div>
                        </el-col>
                    </el-row>
                </el-card>

                <!-- 详细信息区域 -->
                <el-row :gutter="20" class="mt-4">
                    <!-- 配送信息 -->
                    <el-col :span="12">
                        <el-card class="delivery-info-card mb-4" shadow="never">
                            <template #header>
                                <div class="flex items-center space-x-2">
                                    <el-icon class="text-blue-500"><Van /></el-icon>
                                    <span>配送信息</span>
                                </div>
                            </template>

                            <div class="delivery-info">
                                <div class="delivery-type">
                                    <el-tag :type="orderInfo.delivery_type == 1 ? 'primary' : 'success'" size="large">
                                        {{ orderInfo.delivery_type_text }}
                                    </el-tag>
                                </div>

                                <!-- 快递上门信息 -->
                                <div v-if="orderInfo.delivery_type == 1" class="pickup-info">
                                    <div class="info-item">
                                        <span class="label">联系人：</span>
                                        <span class="value">{{ orderInfo.pickup_contact_name || '-' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">联系电话：</span>
                                        <span class="value">{{ orderInfo.pickup_contact_phone || '-' }}</span>
                                        <el-button type="primary" link size="small" @click="copyText(orderInfo.pickup_contact_phone)" v-if="orderInfo.pickup_contact_phone">
                                            复制
                                        </el-button>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">取件地址：</span>
                                        <span class="value">{{ orderInfo.pickupAddress?.full_address || '-' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">期望时间：</span>
                                        <span class="value">{{ orderInfo.pickup_time || '-' }}</span>
                                    </div>
                                </div>

                                <!-- 用户自寄信息 -->
                                <div v-else class="express-info">
                                    <div class="info-item">
                                        <span class="label">快递公司：</span>
                                        <span class="value">{{ orderInfo.express_company || '-' }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">快递单号：</span>
                                        <span class="value">{{ orderInfo.express_number || '-' }}</span>
                                        <el-button type="primary" link size="small" @click="copyText(orderInfo.express_number)" v-if="orderInfo.express_number">
                                            复制
                                        </el-button>
                                    </div>
                                    <div class="info-item">
                                        <span class="label">快递状态：</span>
                                        <el-tag :type="getExpressStatusTagType(orderInfo.express_status)" size="small">
                                            {{ orderInfo.express_status_text }}
                                        </el-tag>
                                    </div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>

                    <!-- 质检信息 -->
                    <el-col :span="12">
                        <el-card class="quality-info-card mb-4" shadow="never">
                            <template #header>
                                <div class="flex items-center space-x-2">
                                    <el-icon class="text-orange-500"><Search /></el-icon>
                                    <span>质检信息</span>
                                </div>
                            </template>

                            <div class="quality-info" v-if="orderInfo.quality_score">
                                <div class="quality-score">
                                    <el-rate v-model="qualityRate" disabled show-score />
                                    <span class="score-text">{{ orderInfo.quality_score }}分</span>
                                </div>
                                <div class="quality-details">
                                    <div class="info-item">
                                        <span class="label">质检员：</span>
                                        <span class="value">{{ orderInfo.quality_admin?.real_name || orderInfo.quality_admin?.username || '-' }}</span>
                                    </div>
                                    <div class="info-item" v-if="orderInfo.quality_note">
                                        <span class="label">质检说明：</span>
                                        <span class="value">{{ orderInfo.quality_note }}</span>
                                    </div>
                                </div>
                                <div class="quality-images" v-if="orderInfo.quality_images?.length">
                                    <div class="label">质检图片：</div>
                                    <div class="images-grid">
                                        <el-image
                                            v-for="(img, index) in orderInfo.quality_images"
                                            :key="index"
                                            :src="img"
                                            class="quality-img"
                                            fit="cover"
                                            :preview-src-list="orderInfo.quality_images"
                                            :initial-index="index"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div v-else class="no-quality">
                                <el-empty description="暂无质检信息" :image-size="80" />
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 订单流程时间轴 -->
                <el-card class="timeline-card mb-4" shadow="never">
                    <template #header>
                        <div class="flex items-center space-x-2">
                            <el-icon class="text-purple-500"><Clock /></el-icon>
                            <span>订单流程</span>
                        </div>
                    </template>

                    <el-timeline class="order-timeline">
                        <el-timeline-item
                            v-for="item in timelineData"
                            :key="item.timestamp"
                            :timestamp="item.timestamp"
                            :type="item.type"
                            :icon="item.icon"
                            :size="item.size || 'normal'"
                        >
                            <div class="timeline-content">
                                <h4 class="timeline-title">{{ item.title }}</h4>
                                <p v-if="item.content" class="timeline-desc">{{ item.content }}</p>
                            </div>
                        </el-timeline-item>
                    </el-timeline>
                </el-card>

                <!-- 备注信息 -->
                <el-card class="notes-card mb-4" shadow="never" v-if="hasNotes">
                    <template #header>
                        <div class="flex items-center space-x-2">
                            <el-icon class="text-gray-500"><Document /></el-icon>
                            <span>备注信息</span>
                        </div>
                    </template>

                    <div class="notes-content">
                        <div class="note-item" v-if="orderInfo.user_note">
                            <div class="note-label">用户备注：</div>
                            <div class="note-value">{{ orderInfo.user_note }}</div>
                        </div>
                        <div class="note-item" v-if="orderInfo.admin_note">
                            <div class="note-label">管理员备注：</div>
                            <div class="note-value">{{ orderInfo.admin_note }}</div>
                        </div>
                        <div class="note-item" v-if="orderInfo.reject_reason">
                            <div class="note-label">拒绝原因：</div>
                            <div class="note-value error">{{ orderInfo.reject_reason }}</div>
                        </div>
                        <div class="note-item" v-if="orderInfo.return_reason">
                            <div class="note-label">退回原因：</div>
                            <div class="note-value warning">{{ orderInfo.return_reason }}</div>
                        </div>
                    </div>
                </el-card>
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
    Refresh, Star, Picture, Box, CopyDocument, Van, User, Money,
    Search, Clock, Document, Check, RefreshLeft
} from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import {
    getRecycleOrderInfo,
    receiveRecycleOrder,
    startQualityCheck,
    completeQualityCheck,
    settlementRecycleOrder,
    returnRecycleOrder
} from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

const route = useRoute()
const router = useRouter()

const loading = ref(true)
const orderInfo = ref<any>({})
const quotePhotos = ref<any[]>([]) // 估价订单照片

// 质检评分（转换为5星制）
const qualityRate = computed(() => {
    return orderInfo.value.quality_score ? Math.round(orderInfo.value.quality_score / 20) : 0
})

// 是否有快速操作
const hasQuickActions = computed(() => {
    const status = orderInfo.value.status
    return status == 2 || status == 3 || status == 4 || status == 5 ||
           (status == 7 && orderInfo.value.settlement_status == 0)
})

// 是否有备注信息
const hasNotes = computed(() => {
    return orderInfo.value.user_note || orderInfo.value.admin_note ||
           orderInfo.value.reject_reason || orderInfo.value.return_reason
})

// 时间轴数据
const timelineData = computed(() => {
    const timeline = []
    const info = orderInfo.value

    // 订单创建
    timeline.push({
        timestamp: info.create_time_text,
        title: '订单创建',
        content: `订单来源：${info.source_type_text}`,
        type: 'primary',
        icon: 'Plus'
    })

    // 实际取件时间
    if (info.pickup_time_actual_text) {
        timeline.push({
            timestamp: info.pickup_time_actual_text,
            title: '商品取件',
            content: '快递员已取件',
            type: 'primary',
            icon: 'Truck'
        })
    }

    // 收货时间
    if (info.receive_time_text) {
        timeline.push({
            timestamp: info.receive_time_text,
            title: '平台收货',
            content: '平台已收到商品',
            type: 'success',
            icon: 'Box'
        })
    }

    // 质检开始时间
    if (info.quality_start_time_text) {
        timeline.push({
            timestamp: info.quality_start_time_text,
            title: '开始质检',
            content: `质检员：${info.quality_admin?.real_name || info.quality_admin?.username || '-'}`,
            type: 'warning',
            icon: 'Search'
        })
    }

    // 质检完成时间
    if (info.quality_complete_time_text) {
        timeline.push({
            timestamp: info.quality_complete_time_text,
            title: '质检完成',
            content: `质检评分：${info.quality_score}分，最终价格：¥${info.final_price}`,
            type: 'success',
            icon: 'Check'
        })
    }

    // 用户确认时间
    if (info.confirm_time_text) {
        if (info.status == 7) {
            timeline.push({
                timestamp: info.confirm_time_text,
                title: '用户确认',
                content: '用户接受质检价格',
                type: 'success',
                icon: 'CircleCheck'
            })
        } else if (info.status == 5) {
            timeline.push({
                timestamp: info.confirm_time_text,
                title: '用户拒绝',
                content: '用户不接受质检价格，申请退回',
                type: 'danger',
                icon: 'CircleClose'
            })
        }
    }

    // 退回时间
    if (info.return_time_text) {
        timeline.push({
            timestamp: info.return_time_text,
            title: '商品退回',
            content: '商品已退回给用户',
            type: 'info',
            icon: 'RefreshLeft'
        })
    }

    // 结算时间
    if (info.settlement_time_text) {
        timeline.push({
            timestamp: info.settlement_time_text,
            title: '订单结算',
            content: `结算金额：¥${info.settlement_amount}，已转入用户余额`,
            type: 'success',
            icon: 'Money'
        })
    }

    return timeline.reverse() // 倒序显示，最新的在上面
})

/**
 * 获取订单详情
 */
const loadOrderInfo = () => {
    loading.value = true
    const id = route.params.id as string

    getRecycleOrderInfo(parseInt(id)).then(res => {
        loading.value = false
        orderInfo.value = res.data
    }).catch(() => {
        loading.value = false
    })
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: number) => {
    const tagTypes: Record<number, string> = {
        1: 'warning',  // 待取件
        2: 'primary',  // 待收货
        3: 'info',     // 待质检
        4: 'warning',  // 待确认
        5: 'danger',   // 待退回
        6: 'info',     // 已退回
        7: 'success'   // 已完成
    }
    return tagTypes[status] || 'info'
}

/**
 * 获取订单来源标签类型
 */
const getSourceTypeTagType = (sourceType: number) => {
    const tagTypes: Record<number, string> = {
        1: 'primary',   // 估价订单
        2: 'success',   // 直接回收
        3: 'warning'    // 批量下单
    }
    return tagTypes[sourceType] || 'info'
}

/**
 * 获取订单来源图标
 */
const getSourceTypeIcon = (sourceType: number) => {
    const icons: Record<number, string> = {
        1: 'Star',      // 估价订单
        2: 'Picture',   // 直接回收
        3: 'Box'        // 批量下单
    }
    return icons[sourceType] || 'Picture'
}

/**
 * 获取订单来源图标样式类
 */
const getSourceTypeIconClass = (sourceType: number) => {
    const classes: Record<number, string> = {
        1: 'text-blue-500',    // 估价订单
        2: 'text-green-500',   // 直接回收
        3: 'text-orange-500'   // 批量下单
    }
    return classes[sourceType] || 'text-gray-500'
}

/**
 * 获取订单来源标题
 */
const getSourceTypeTitle = (sourceType: number) => {
    const titles: Record<number, string> = {
        1: '估价订单信息',
        2: '直接回收商品',
        3: '批量回收订单'
    }
    return titles[sourceType] || '商品信息'
}

/**
 * 获取商品图片
 */
const getProductImage = () => {
    if (orderInfo.value.source_type == 1) {
        return orderInfo.value.brand?.logo || orderInfo.value.product_image
    }
    return orderInfo.value.product_image
}

/**
 * 获取快递状态标签类型
 */
const getExpressStatusTagType = (status: number) => {
    const tagTypes: Record<number, string> = {
        0: 'info',      // 暂无轨迹
        1: 'warning',   // 已揽收
        2: 'primary',   // 运输中
        3: 'success',   // 已签收
        4: 'danger'     // 异常
    }
    return tagTypes[status] || 'info'
}

/**
 * 获取会员等级文本
 */
const getMemberLevelText = (level: number) => {
    const levels: Record<number, string> = {
        1: '普通会员',
        2: 'VIP会员',
        3: '黄金会员',
        4: '钻石会员'
    }
    return levels[level] || '普通会员'
}

/**
 * 刷新详情
 */
const refreshDetail = () => {
    loadOrderInfo()
    if (orderInfo.value.quote_order_id) {
        loadQuotePhotos()
    }
}

/**
 * 复制文本
 */
const copyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('复制成功')
    }).catch(() => {
        ElMessage.error('复制失败')
    })
}

/**
 * 查看估价订单
 */
const viewQuoteOrder = (quoteOrderId: number) => {
    router.push(`/yz_she/quote-order/detail/${quoteOrderId}`)
}

/**
 * 查看商品详情
 */
const viewProductDetail = (productId: number) => {
    router.push(`/shop/goods/detail/${productId}`)
}

/**
 * 加载估价订单照片
 */
const loadQuotePhotos = () => {
    if (!orderInfo.value.quote_order_id) return

    // 这里应该调用获取估价订单照片的API
    // getQuoteOrderPhotos(orderInfo.value.quote_order_id).then(res => {
    //     quotePhotos.value = res.data
    // })
}

/**
 * 预览图片
 */
const previewImages = (photos: any[], currentPhoto: any) => {
    const urls = photos.map(photo => photo.photo_url)
    const currentIndex = photos.findIndex(photo => photo.id === currentPhoto.id)

    // 这里可以使用Element Plus的图片预览组件
    // 或者自定义图片预览功能
    console.log('预览图片', urls, currentIndex)
}

/**
 * 返回列表
 */
const goBack = () => {
    router.go(-1)
}

/**
 * 收货确认
 */
const receiveOrder = () => {
    ElMessageBox.prompt('请输入收货备注', '收货确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入收货备注（可选）'
    }).then(({ value }) => {
        const params = {
            receive_note: value || '',
            admin_id: 1 // 这里应该从用户信息中获取
        }
        receiveRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('收货成功')
            loadOrderInfo()
        })
    })
}

/**
 * 开始质检
 */
const startQuality = () => {
    ElMessageBox.confirm('确认开始质检该订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1 // 这里应该从用户信息中获取
        }
        startQualityCheck(orderInfo.value.id, params).then(() => {
            ElMessage.success('开始质检成功')
            loadOrderInfo()
        })
    })
}

/**
 * 完成质检
 */
const completeQuality = () => {
    router.push(`/yz_she/recycle_order/quality/${orderInfo.value.id}`)
}

/**
 * 结算订单
 */
const settlement = () => {
    ElMessageBox.confirm('确认结算该订单？结算后金额将直接转入用户余额。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            settlement_note: '订单结算完成'
        }
        settlementRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('结算成功')
            loadOrderInfo()
        })
    })
}

/**
 * 退回订单
 */
const returnOrder = () => {
    ElMessageBox.prompt('请输入退回原因', '确认退回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '退回原因不能为空'
    }).then(({ value }) => {
        const params = {
            admin_id: 1, // 这里应该从用户信息中获取
            return_note: value
        }
        returnRecycleOrder(orderInfo.value.id, params).then(() => {
            ElMessage.success('退回成功')
            loadOrderInfo()
        })
    })
}

/**
 * 处理状态操作
 */
const handleStatusAction = (actionKey: string, orderInfo: any) => {
    switch (actionKey) {
        case 'update_express':
            // 更新快递信息
            console.log('更新快递信息', orderInfo)
            break
        case 'receive_order':
            receiveOrder()
            break
        case 'start_quality':
            startQuality()
            break
        case 'complete_quality':
            completeQuality()
            break
        case 'settlement':
            settlement()
            break
        default:
            console.log('未知操作:', actionKey)
    }
}

onMounted(() => {
    loadOrderInfo()
})
</script>

<style lang="scss" scoped>
// 基础颜色
.text-primary { color: #409eff; }
.text-success { color: #67c23a; }
.text-warning { color: #e6a23c; }
.text-danger { color: #f56c6c; }

// 订单概览卡片
.order-overview-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .order-overview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;

        .order-basic-info {
            .order-number {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                h2 {
                    margin: 0;
                    font-size: 20px;
                    font-weight: 600;
                    color: #303133;
                }
            }

            .order-meta {
                display: flex;
                align-items: center;
                gap: 12px;
                color: #909399;
                font-size: 14px;

                .divider {
                    color: #dcdfe6;
                }
            }
        }

        .order-status-info {
            text-align: right;

            .status-tag {
                margin-bottom: 8px;
            }
        }
    }
}

// 快速操作卡片
.quick-actions-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .quick-actions {
        display: flex;
        gap: 12px;
        padding: 16px 20px;
        justify-content: center;
    }
}

// 商品信息卡片
.product-info-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .product-info {
        display: flex;
        gap: 16px;
        padding: 20px;

        .product-image {
            .product-img {
                width: 80px;
                height: 80px;
                border-radius: 8px;
            }

            .image-placeholder {
                width: 80px;
                height: 80px;
                background: #f5f7fa;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
            }
        }

        .product-details {
            flex: 1;

            .product-name {
                margin: 0 0 12px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
            }

            .product-meta {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                margin-bottom: 12px;

                .meta-item {
                    display: flex;

                    .label {
                        color: #909399;
                        min-width: 60px;
                    }

                    .value {
                        color: #606266;
                        font-weight: 500;
                    }
                }
            }

            .special-note {
                margin-top: 12px;
            }
        }
    }
}

// 用户信息卡片
.user-info-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .user-info {
        display: flex;
        gap: 16px;
        padding: 20px;
        align-items: center;

        .user-avatar {
            flex-shrink: 0;
        }

        .user-details {
            flex: 1;

            .user-name {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
            }

            .user-meta {
                .meta-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 4px;

                    .label {
                        color: #909399;
                        min-width: 50px;
                    }

                    .value {
                        color: #606266;
                    }
                }
            }
        }
    }
}

// 价格信息卡片
.price-info-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .price-info {
        padding: 20px;

        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            &.total {
                border-top: 1px solid #ebeef5;
                padding-top: 16px;
                margin-bottom: 0;
            }

            .price-label {
                color: #909399;
                font-size: 14px;
            }

            .price-value {
                font-size: 18px;
                font-weight: 600;

                &.primary { color: #409eff; }
                &.success { color: #67c23a; }
                &.warning { color: #e6a23c; }
                &.danger { color: #f56c6c; }
            }
        }

        .price-note {
            margin-top: 16px;
        }
    }
}

// 配送信息卡片
.delivery-info-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .delivery-info {
        padding: 20px;

        .delivery-type {
            margin-bottom: 16px;
        }

        .pickup-info,
        .express-info {
            .info-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                .label {
                    color: #909399;
                    min-width: 80px;
                }

                .value {
                    color: #606266;
                    flex: 1;
                }
            }
        }
    }
}

// 质检信息卡片
.quality-info-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .quality-info {
        padding: 20px;

        .quality-score {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;

            .score-text {
                font-weight: 600;
                color: #e6a23c;
            }
        }

        .quality-details {
            margin-bottom: 16px;

            .info-item {
                display: flex;
                margin-bottom: 8px;

                .label {
                    color: #909399;
                    min-width: 80px;
                }

                .value {
                    color: #606266;
                    flex: 1;
                }
            }
        }

        .quality-images {
            .label {
                color: #909399;
                margin-bottom: 8px;
                display: block;
            }

            .images-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
                gap: 8px;

                .quality-img {
                    width: 60px;
                    height: 60px;
                    border-radius: 4px;
                    cursor: pointer;
                }
            }
        }
    }

    .no-quality {
        padding: 40px 20px;
        text-align: center;
    }
}

// 时间轴卡片
.timeline-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .order-timeline {
        padding: 20px;

        .timeline-content {
            .timeline-title {
                margin: 0 0 4px 0;
                font-size: 14px;
                font-weight: 600;
                color: #303133;
            }

            .timeline-desc {
                margin: 0;
                font-size: 12px;
                color: #909399;
            }
        }
    }
}

// 备注信息卡片
.notes-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .notes-content {
        padding: 20px;

        .note-item {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            .note-label {
                color: #909399;
                font-size: 14px;
                margin-bottom: 4px;
            }

            .note-value {
                color: #606266;
                line-height: 1.5;

                &.error {
                    color: #f56c6c;
                }

                &.warning {
                    color: #e6a23c;
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .order-overview {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 16px;

        .order-status-info {
            text-align: left !important;
        }
    }

    .quick-actions {
        flex-wrap: wrap;
        justify-content: flex-start !important;
    }

    .product-info {
        flex-direction: column;
    }

    .user-info {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .product-meta {
        grid-template-columns: 1fr !important;
    }
}
</style>
