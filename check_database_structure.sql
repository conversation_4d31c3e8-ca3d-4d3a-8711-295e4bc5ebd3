-- 检查数据库表结构和数据
-- 用于排查回收订单接口问题

-- 1. 检查回收订单表结构
DESCRIBE `www_yz_she_recycle_orders`;

-- 2. 检查是否有数据
SELECT COUNT(*) as total_orders FROM `www_yz_she_recycle_orders`;

-- 3. 检查前5条数据
SELECT 
    `id`,
    `order_no`,
    `member_id`,
    `status`,
    `source_type`,
    `delivery_type`,
    `settlement_status`,
    `express_status`,
    `create_time`
FROM `www_yz_she_recycle_orders` 
LIMIT 5;

-- 4. 检查关联表是否存在
-- 检查分类表
SELECT COUNT(*) as category_count FROM `www_yz_she_categories`;
SELECT `id`, `name`, `image` FROM `www_yz_she_categories` LIMIT 3;

-- 检查品牌表
SELECT COUNT(*) as brand_count FROM `www_yz_she_brands`;
SELECT `id`, `name`, `logo` FROM `www_yz_she_brands` LIMIT 3;

-- 检查商品表
SELECT COUNT(*) as goods_count FROM `www_yz_she_goods`;
SELECT `id`, `name`, `image`, `code` FROM `www_yz_she_goods` LIMIT 3;

-- 检查会员地址表
SELECT COUNT(*) as address_count FROM `www_member_address`;
SELECT `id`, `name`, `mobile`, `full_address` FROM `www_member_address` LIMIT 3;

-- 检查加价券表
SELECT COUNT(*) as voucher_count FROM `www_yz_she_voucher`;
SELECT `id`, `voucher_no`, `price`, `title` FROM `www_yz_she_voucher` LIMIT 3;

-- 5. 检查是否有express_status字段
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'www_yz_she_recycle_orders' 
  AND COLUMN_NAME = 'express_status';
