-- 检查缺失的字段

-- 1. 检查表结构
DESCRIBE `www_yz_she_recycle_orders`;

-- 2. 检查是否有express_status字段
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'www_yz_she_recycle_orders' 
  AND COLUMN_NAME IN ('express_status', 'settlement_status');

-- 3. 查看原始数据（不带条件）
SELECT COUNT(*) as total FROM `www_yz_she_recycle_orders`;

-- 4. 查看前5条数据
SELECT 
    `id`,
    `order_no`,
    `status`,
    `create_time`
FROM `www_yz_she_recycle_orders` 
ORDER BY `id` DESC 
LIMIT 5;
