<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\adminapi\controller\recycle;

use addon\yz_she\app\service\admin\recycle\RecycleOrderService;
use core\base\BaseAdminController;
use think\Response;

/**
 * 回收订单控制器
 * Class RecycleOrder
 * @package addon\yz_she\app\adminapi\controller\recycle
 */
class RecycleOrder extends BaseAdminController
{
    /**
     * 获取回收订单列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['search_type', ''],
            ['search_name', ''],
            ['keyword', ''],
            ['status', ''],
            ['source_type', ''],
            ['delivery_type', ''],
            ['create_time', []],
            ['settlement_time', []],
        ]);
        return success((new RecycleOrderService())->getPage($data));
    }

    /**
     * 获取回收订单详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new RecycleOrderService())->getInfo($id));
    }

    /**
     * 获取状态统计数据
     * @return Response
     */
    public function getStatusCounts()
    {
        return success((new RecycleOrderService())->getStatusCounts());
    }

    /**
     * 获取状态选项
     * @return Response
     */
    public function getStatusOptions()
    {
        return success((new RecycleOrderService())->getStatusOptions());
    }

    /**
     * 获取来源类型选项
     * @return Response
     */
    public function getSourceTypeOptions()
    {
        return success((new RecycleOrderService())->getSourceTypeOptions());
    }

    /**
     * 获取配送方式选项
     * @return Response
     */
    public function getDeliveryTypeOptions()
    {
        return success((new RecycleOrderService())->getDeliveryTypeOptions());
    }

    /**
     * 收货确认
     * @param int $id
     * @return Response
     */
    public function receive(int $id)
    {
        $data = $this->request->params([
            ['receive_status', 1],
            ['receive_note', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->receiveOrder($id, $data);
        return success('收货成功');
    }

    /**
     * 开始质检
     * @param int $id
     * @return Response
     */
    public function startQuality(int $id)
    {
        $data = $this->request->params([
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->startQualityCheck($id, $data);
        return success('开始质检成功');
    }

    /**
     * 完成质检
     * @param int $id
     * @return Response
     */
    public function completeQuality(int $id)
    {
        $data = $this->request->params([
            ['quality_score', 0],
            ['final_price', 0],
            ['quality_note', ''],
            ['quality_images', []],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->completeQualityCheck($id, $data);
        return success('质检完成');
    }

    /**
     * 订单结算
     * @param int $id
     * @return Response
     */
    public function settlement(int $id)
    {
        $data = $this->request->params([
            ['settlement_note', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->settlementOrder($id, $data);
        return success('结算成功');
    }

    /**
     * 退回订单
     * @param int $id
     * @return Response
     */
    public function returnOrder(int $id)
    {
        $data = $this->request->params([
            ['return_note', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->returnOrder($id, $data);
        return success('退回成功');
    }

    /**
     * 更新快递信息
     * @param int $id
     * @return Response
     */
    public function updateExpress(int $id)
    {
        $data = $this->request->params([
            ['express_company', ''],
            ['express_number', ''],
            ['express_fee', 0],
            ['express_note', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->updateExpressInfo($id, $data);
        return success('快递信息更新成功');
    }

    /**
     * 更新订单备注
     * @param int $id
     * @return Response
     */
    public function updateNotes(int $id)
    {
        $data = $this->request->params([
            ['admin_note', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->updateOrderNotes($id, $data);
        return success('备注更新成功');
    }

    /**
     * 获取订单日志
     * @param int $id
     * @return Response
     */
    public function getLogs(int $id)
    {
        return success((new RecycleOrderService())->getOrderLogs($id));
    }

    /**
     * 批量操作
     * @return Response
     */
    public function batch()
    {
        $data = $this->request->params([
            ['ids', []],
            ['action', ''],
            ['admin_id', 0]
        ]);
        (new RecycleOrderService())->batchOperation($data);
        return success('批量操作成功');
    }

    /**
     * 导出订单数据
     * @return Response
     */
    public function export()
    {
        $data = $this->request->params([
            ['search_type', ''],
            ['search_name', ''],
            ['keyword', ''],
            ['status', ''],
            ['source_type', ''],
            ['delivery_type', ''],
            ['create_time', []],
            ['settlement_time', []],
        ]);

        $exportData = (new RecycleOrderService())->exportData($data);

        // 这里应该返回Excel文件下载
        // 简化处理，直接返回数据
        return success($exportData);
    }
}