<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\adminapi\controller\recycle;

use addon\yz_she\app\service\admin\recycle\RecycleStandardService;
use core\base\BaseAdminController;
use think\Response;

/**
 * 回收标准控制器
 * Class RecycleStandard
 * @package addon\yz_she\app\adminapi\controller\recycle
 */
class RecycleStandard extends BaseAdminController
{
    /**
     * 回收标准列表
     * @return Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['category_id', ''],
            ['title', ''],
            ['status', ''],
            ['page', 1],
            ['limit', 10]
        ]);
        return success((new RecycleStandardService())->getPage($data));
    }

    /**
     * 回收标准详情
     * @param int $id
     * @return Response
     */
    public function info(int $id)
    {
        return success((new RecycleStandardService())->getInfo($id));
    }

    /**
     * 添加回收标准
     * @return Response
     */
    public function add()
    {
        $data = $this->request->params([
            ['category_id', 0],
            ['title', ''],
            ['image', ''],
            ['description', ''],
            ['sort', 0],
            ['status', 1]
        ]);
        $this->validate($data, 'addon\yz_she\app\validate\recycle\RecycleStandard.add');
        $id = (new RecycleStandardService())->add($data);
        return success('添加成功', ['id' => $id]);
    }

    /**
     * 编辑回收标准
     * @param int $id
     * @return Response
     */
    public function edit(int $id)
    {
        $data = $this->request->params([
            ['category_id', 0],
            ['title', ''],
            ['image', ''],
            ['description', ''],
            ['sort', 0],
            ['status', 1]
        ]);
        $this->validate($data, 'addon\yz_she\app\validate\recycle\RecycleStandard.edit');
        (new RecycleStandardService())->edit($id, $data);
        return success('编辑成功');
    }

    /**
     * 删除回收标准
     * @param int $id
     * @return Response
     */
    public function del(int $id)
    {
        (new RecycleStandardService())->del($id);
        return success('删除成功');
    }

    /**
     * 修改状态
     * @param int $id
     * @return Response
     */
    public function modifyStatus(int $id)
    {
        $data = $this->request->params([
            ['status', 1]
        ]);
        (new RecycleStandardService())->modifyStatus($id, $data['status']);
        return success('修改成功');
    }

    /**
     * 批量操作
     * @return Response
     */
    public function batch()
    {
        $data = $this->request->params([
            ['ids', []],
            ['action', '']
        ]);
        (new RecycleStandardService())->batch($data);
        return success('操作成功');
    }
}