<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

use think\facade\Route;

use app\adminapi\middleware\AdminCheckRole;
use app\adminapi\middleware\AdminCheckToken;
use app\adminapi\middleware\AdminLog;

/**
 * 柚子奢侈品回收
 */
Route::group('yz_she', function () {

     /***************************************************** hello world ****************************************************/
    Route::get('hello_world', 'addon\yz_she\app\adminapi\controller\hello_world\Index@index');

    /***************************************************** 商品分类 ****************************************************/
    // 商品分类分页列表
    Route::get('goods/category', 'addon\yz_she\app\adminapi\controller\goods\Category@pages');
    // 商品分类列表
    Route::get('goods/category/list', 'addon\yz_she\app\adminapi\controller\goods\Category@lists');
    // 商品分类详情
    Route::get('goods/category/:id', 'addon\yz_she\app\adminapi\controller\goods\Category@info');
    // 商品分类添加
    Route::post('goods/category', 'addon\yz_she\app\adminapi\controller\goods\Category@add');
    // 商品分类编辑
    Route::put('goods/category/:id', 'addon\yz_she\app\adminapi\controller\goods\Category@edit');
    // 商品分类删除
    Route::delete('goods/category/:id', 'addon\yz_she\app\adminapi\controller\goods\Category@del');
    // 商品分类排序
    Route::put('goods/category/sort', 'addon\yz_she\app\adminapi\controller\goods\Category@modifySort');

    /***************************************************** 商品品牌 ****************************************************/
    // 商品品牌分页列表
    Route::get('goods/brand', 'addon\yz_she\app\adminapi\controller\goods\Brand@pages');
    // 商品品牌列表
    Route::get('goods/brand/list', 'addon\yz_she\app\adminapi\controller\goods\Brand@lists');
    // 商品品牌详情
    Route::get('goods/brand/:id', 'addon\yz_she\app\adminapi\controller\goods\Brand@info');
    // 商品品牌添加
    Route::post('goods/brand', 'addon\yz_she\app\adminapi\controller\goods\Brand@add');
    // 商品品牌编辑
    Route::put('goods/brand/:id', 'addon\yz_she\app\adminapi\controller\goods\Brand@edit');
    // 商品品牌删除
    Route::delete('goods/brand/:id', 'addon\yz_she\app\adminapi\controller\goods\Brand@del');
    // 商品品牌排序
    Route::put('goods/brand/sort', 'addon\yz_she\app\adminapi\controller\goods\Brand@modifySort');
    // 设置热门品牌
    Route::put('goods/brand/hot', 'addon\yz_she\app\adminapi\controller\goods\Brand@setHot');

    /***************************************************** 商品管理 ****************************************************/
    // 商品分页列表
    Route::get('goods', 'addon\yz_she\app\adminapi\controller\goods\Goods@pages');
    // 商品列表
    Route::get('goods/list', 'addon\yz_she\app\adminapi\controller\goods\Goods@lists');
    // 商品详情
    Route::get('goods/:id', 'addon\yz_she\app\adminapi\controller\goods\Goods@info');
    // 商品添加
    Route::post('goods', 'addon\yz_she\app\adminapi\controller\goods\Goods@add');
    // 商品编辑
    Route::put('goods/:id', 'addon\yz_she\app\adminapi\controller\goods\Goods@edit');
    // 商品删除
    Route::delete('goods/:id', 'addon\yz_she\app\adminapi\controller\goods\Goods@del');
    // 商品排序
    Route::put('goods/sort', 'addon\yz_she\app\adminapi\controller\goods\Goods@modifySort');
    // 设置热门商品
    Route::put('goods/hot', 'addon\yz_she\app\adminapi\controller\goods\Goods@setHot');
    // 修改商品状态
    Route::put('goods/status', 'addon\yz_she\app\adminapi\controller\goods\Goods@modifyStatus');

    /***************************************************** 加价券管理 ****************************************************/
    // 加价券列表
    Route::get('voucher', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@lists');
    // 加价券详情
    Route::get('voucher/:id', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@info');
    // 加价券添加
    Route::post('voucher', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@add');
    // 加价券编辑
    Route::put('voucher/:id', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@edit');
    // 加价券删除
    Route::delete('voucher/:id', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@del');
    // 关闭加价券
    Route::put('voucher/close/:id', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@close');
    // 获取加价券状态
    Route::get('voucher/status', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@status');
    // 发放给全部用户
    Route::post('voucher/send_all', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@sendAll');
    // 发放给指定用户
    Route::post('voucher/send_users', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@sendUsers');
    // 获取发放记录
    Route::get('voucher/send_records', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@sendRecords');
    // 获取发放详情
    Route::get('voucher/send_detail/:id', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@sendDetail');
    // 批量删除
    Route::delete('voucher/batch', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@batchDel');
    // 批量关闭
    Route::put('voucher/batch_close', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@batchClose');
    // 获取领取记录
    Route::get('voucher/receive_records', 'addon\yz_she\app\adminapi\controller\voucher\Voucher@receiveRecords');

    /***************************************************** 回收标准管理 ****************************************************/
    // 回收标准分页列表
    Route::get('recycle_standard', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@lists');
    // 回收标准详情
    Route::get('recycle_standard/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@info');
    // 回收标准添加
    Route::post('recycle_standard', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@add');
    // 回收标准编辑
    Route::put('recycle_standard/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@edit');
    // 回收标准删除
    Route::delete('recycle_standard/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@del');
    // 回收标准状态更新
    Route::put('recycle_standard/status/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleStandard@modifyStatus');

    /***************************************************** 分类配置管理 ****************************************************/
    // 统一配置接口
    Route::get('category/config', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getCategoryConfig');

    // 照片配置
    Route::get('category/photos', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getPhotoList');
    Route::get('category/photos/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getPhotoInfo');
    Route::post('category/photos', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@addPhoto');
    Route::put('category/photos/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@editPhoto');
    Route::delete('category/photos/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@deletePhoto');
    Route::put('category/photos/sort', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@updatePhotoSort');

    // 配件配置
    Route::get('category/accessories', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getAccessoryList');
    Route::get('category/accessories/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getAccessoryInfo');
    Route::post('category/accessories', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@addAccessory');
    Route::put('category/accessories/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@editAccessory');
    Route::delete('category/accessories/:id', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@deleteAccessory');
    Route::put('category/accessories/sort', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@updateAccessorySort');

    // 分类选项
    Route::get('category/options', 'addon\yz_she\app\adminapi\controller\config\CategoryConfig@getCategoryOptions');

    /***************************************************** 估价订单管理 ****************************************************/
    // 估价订单列表
    Route::get('quote_order/lists', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@lists');
    // 估价订单详情
    Route::get('quote_order/info/:id', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@info');
    // 估价操作
    Route::post('quote_order/quote/:id', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@quote');
    // 取消订单
    Route::post('quote_order/cancel/:id', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@cancel');
    // 完成订单
    Route::post('quote_order/complete/:id', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@complete');

    // 获取状态选项
    Route::get('quote_order/get_status_options', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@getStatusOptions');
    // 批量操作
    Route::post('quote_order/batch', 'addon\yz_she\app\adminapi\controller\order\QuoteOrder@batch');

    /***************************************************** 会员收货地址管理 ****************************************************/
    // 会员收货地址列表
    Route::get('member/address', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@lists');
    // 会员收货地址详情
    Route::get('member/address/:id', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@info');
    // 添加会员收货地址
    Route::post('member/address', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@add');
    // 编辑会员收货地址
    Route::put('member/address/:id', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@edit');
    // 删除会员收货地址
    Route::delete('member/address/:id', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@del');
    // 获取会员选项
    Route::get('member/address/member_options', 'addon\yz_she\app\adminapi\controller\member\MemberAddress@getMemberOptions');

    

    /***************************************************** 物流回调日志管理 ****************************************************/
    // 物流日志列表
    Route::get('express_log/lists', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@lists');
    // 物流日志详情
    Route::get('express_log/info/:id', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@info');
    // 删除物流日志
    Route::delete('express_log/:id', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@del');
    // 批量删除物流日志
    Route::delete('express_log/batch', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@batch');
    // 导出物流日志
    Route::get('express_log/export', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@export');
    // 获取状态码选项
    Route::get('express_log/get_type_code_options', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@getTypeCodeOptions');
    // 获取扣费状态选项
    Route::get('express_log/get_fee_over_options', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@getFeeOverOptions');
    // 获取统计数据
    Route::get('express_log/get_statistics', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@getStatistics');
    // 根据运单号获取最新日志
    Route::get('express_log/get_latest_by_waybill', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@getLatestByWaybill');
    // 根据订单ID获取物流日志
    Route::get('express_log/get_by_order_id', 'addon\yz_she\app\adminapi\controller\express\ExpressLog@getByOrderId');

    /***************************************************** 回收订单管理 ****************************************************/
    // 回收订单列表
    Route::get('recycle_order/lists', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@lists');
    // 回收订单详情
    Route::get('recycle_order/info/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@info');
    // 获取状态统计数据
    Route::get('recycle_order/status_counts', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getStatusCounts');
    // 获取状态选项
    Route::get('recycle_order/get_status_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getStatusOptions');
    // 获取来源类型选项
    Route::get('recycle_order/get_source_type_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getSourceTypeOptions');
    // 获取配送方式选项
    Route::get('recycle_order/get_delivery_type_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getDeliveryTypeOptions');
    // 获取快递状态选项
    Route::get('recycle_order/get_express_status_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getExpressStatusOptions');
    // 获取分类选项
    Route::get('recycle_order/get_category_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getCategoryOptions');
    // 获取品牌选项
    Route::get('recycle_order/get_brand_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getBrandOptions');
    // 获取商品选项
    Route::get('recycle_order/get_product_options', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getProductOptions');
    // 收货确认
    Route::post('recycle_order/receive/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@receive');
    // 开始质检
    Route::post('recycle_order/start_quality/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@startQuality');
    // 完成质检
    Route::post('recycle_order/complete_quality/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@completeQuality');
    // 订单结算
    Route::post('recycle_order/settlement/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@settlement');
    // 退回订单
    Route::post('recycle_order/return/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@returnOrder');
    // 更新快递信息
    Route::post('recycle_order/update_express/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@updateExpress');
    // 更新订单备注
    Route::post('recycle_order/update_notes/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@updateNotes');
    // 获取订单日志
    Route::get('recycle_order/logs/:id', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@getLogs');
    // 批量操作
    Route::post('recycle_order/batch', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@batch');
    // 导出订单数据
    Route::get('recycle_order/export', 'addon\yz_she\app\adminapi\controller\recycle\RecycleOrder@export');

})->middleware([
    AdminCheckToken::class,
    AdminCheckRole::class,
    AdminLog::class
]);