<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\api\controller\recycle;

use core\base\BaseApiController;
use addon\yz_she\app\service\api\recycle\RecycleOrderService;

/**
 * 回收订单控制器
 */
class RecycleOrder extends BaseApiController
{
    /**
     * 创建回收订单
     * @return \think\Response
     */
    public function create()
    {
        $data = $this->request->params([
            ['quote_order_id', 0],
            ['category_id', 0],
            ['brand_id', 0],
            ['product_id', 0],
            ['product_name', ''],
            ['product_code', ''],
            ['product_image', ''],
            ['pickup_address_id', 0],
            ['voucher_id', 0],
            ['expected_price', 0],
            ['voucher_amount', 0],
            ['final_price', 0],
            ['total_amount', 0],
            ['express_fee', 0],
            ['source_type', 1],
            ['delivery_type', 1],
            ['pickup_contact_name', ''],
            ['pickup_contact_phone', ''],
            ['pickup_address_detail', ''],
            ['pickup_time', ''],
            ['express_company', ''],
            ['express_number', ''],
            ['status', 1],
            ['quantity', 1],
            ['admin_note', '']
        ]);
        
        return success('SUCCESS', (new RecycleOrderService())->create($data));
    }

    /**
     * 获取回收订单列表
     * @return \think\Response
     */
    public function lists()
    {
        $data = $this->request->params([
            ['order_no', ''],
            ['status', ''],
            ['source_type', ''],
            ['delivery_type', ''],
            ['page', 1],
            ['limit', 10]
        ]);
        
        return success('SUCCESS', (new RecycleOrderService())->getPage($data));
    }

    /**
     * 获取回收订单详情
     * @param int $id
     * @return \think\Response
     */
    public function detail(int $id)
    {
        return success('SUCCESS', (new RecycleOrderService())->getInfo($id));
    }

    /**
     * 更新快递信息
     * @param int $id
     * @return \think\Response
     */
    public function updateExpress(int $id)
    {
        $data = $this->request->params([
            ['express_company', ''],
            ['express_number', '']
        ]);
        
        return success('SUCCESS', (new RecycleOrderService())->updateExpress($id, $data));
    }

    /**
     * 确认收货
     * @param int $id
     * @return \think\Response
     */
    public function confirmReceive(int $id)
    {
        return success('SUCCESS', (new RecycleOrderService())->confirmReceive($id));
    }

    /**
     * 确认结算
     * @param int $id
     * @return \think\Response
     */
    public function confirmSettlement(int $id)
    {
        return success('SUCCESS', (new RecycleOrderService())->confirmSettlement($id));
    }

    /**
     * 申请退回
     * @param int $id
     * @return \think\Response
     */
    public function requestReturn(int $id)
    {
        $data = $this->request->params([
            ['reason', '']
        ]);
        
        return success('SUCCESS', (new RecycleOrderService())->requestReturn($id, $data['reason']));
    }

    /**
     * 取消订单
     * @param int $id
     * @return \think\Response
     */
    public function cancel(int $id)
    {
        $data = $this->request->params([
            ['reason', '']
        ]);
        
        return success('SUCCESS', (new RecycleOrderService())->cancel($id, $data['reason']));
    }

    /**
     * 获取订单状态选项
     * @return \think\Response
     */
    public function getStatus()
    {
        return success('SUCCESS', (new RecycleOrderService())->getStatusOptions());
    }

    /**
     * 获取配送方式选项
     * @return \think\Response
     */
    public function getDeliveryTypes()
    {
        return success('SUCCESS', (new RecycleOrderService())->getDeliveryTypeOptions());
    }

    /**
     * 获取订单来源选项
     * @return \think\Response
     */
    public function getSourceTypes()
    {
        return success('SUCCESS', (new RecycleOrderService())->getSourceTypeOptions());
    }
}
