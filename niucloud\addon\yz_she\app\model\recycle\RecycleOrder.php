<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\recycle;

use core\base\BaseModel;
use think\model\concern\SoftDelete;

/**
 * 回收订单模型
 * Class RecycleOrder
 * @package addon\yz_she\app\model\recycle
 */
class RecycleOrder extends BaseModel
{
    use SoftDelete;

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_recycle_orders';

    /**
     * 软删除字段
     * @var string
     */
    protected $deleteTime = 'delete_time';

    /**
     * 软删除默认值
     * @var int
     */
    protected $defaultSoftDelete = 0;

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string
     */
    protected $updateTime = 'update_time';

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'quote_order_id' => 'integer',
        'member_id' => 'integer',
        'category_id' => 'integer',
        'brand_id' => 'integer',
        'product_id' => 'integer',
        'pickup_address_id' => 'integer',
        'voucher_id' => 'integer',
        'expected_price' => 'float',
        'voucher_amount' => 'float',
        'final_price' => 'float',
        'total_amount' => 'float',
        'express_fee' => 'float',
        'status' => 'integer',
        'source_type' => 'integer',
        'delivery_type' => 'integer',
        'settlement_status' => 'integer',
        'express_status' => 'integer',
        'quantity' => 'integer',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp',
        'pickup_time' => 'timestamp',
        'receive_time' => 'timestamp',
        'quality_start_time' => 'timestamp',
        'quality_complete_time' => 'timestamp',
        'return_time' => 'timestamp',
        'settlement_time' => 'timestamp',
        'delete_time' => 'timestamp'
    ];

    /**
     * 订单状态
     */
    const STATUS_PICKUP_PENDING = 1;    // 待取件
    const STATUS_RECEIVE_PENDING = 2;   // 待收货
    const STATUS_QUALITY_PENDING = 3;   // 待质检
    const STATUS_CONFIRM_PENDING = 4;   // 待确认
    const STATUS_RETURN_PENDING = 5;    // 待退回
    const STATUS_RETURNED = 6;          // 已退回
    const STATUS_COMPLETED = 7;         // 已完成

    /**
     * 订单来源类型
     */
    const SOURCE_TYPE_ESTIMATE = 1;     // 估价回收
    const SOURCE_TYPE_DIRECT = 2;       // 直接回收
    const SOURCE_TYPE_BATCH = 3;        // 批量回收

    /**
     * 配送方式
     */
    const DELIVERY_TYPE_EXPRESS = 1;    // 快递上门
    const DELIVERY_TYPE_SELF = 2;       // 用户自寄

    /**
     * 结算状态
     */
    const SETTLEMENT_STATUS_PENDING = 0; // 未结算
    const SETTLEMENT_STATUS_COMPLETED = 1; // 已结算

    /**
     * 快递状态
     */
    const EXPRESS_STATUS_NONE = 0;      // 暂无轨迹
    const EXPRESS_STATUS_PICKED = 1;    // 已揽收
    const EXPRESS_STATUS_TRANSIT = 2;   // 运输中
    const EXPRESS_STATUS_DELIVERED = 3; // 已签收
    const EXPRESS_STATUS_EXCEPTION = 4; // 异常

    /**
     * 获取状态文本
     * @param int $status
     * @return string
     */
    public static function getStatusText($status)
    {
        $statusTexts = [
            self::STATUS_PICKUP_PENDING => '待取件',
            self::STATUS_RECEIVE_PENDING => '待收货',
            self::STATUS_QUALITY_PENDING => '待质检',
            self::STATUS_CONFIRM_PENDING => '待确认',
            self::STATUS_RETURN_PENDING => '待退回',
            self::STATUS_RETURNED => '已退回',
            self::STATUS_COMPLETED => '已完成'
        ];
        return $statusTexts[$status] ?? '未知状态';
    }

    /**
     * 获取来源类型文本
     * @param int $sourceType
     * @return string
     */
    public static function getSourceTypeText($sourceType)
    {
        $sourceTypeTexts = [
            self::SOURCE_TYPE_ESTIMATE => '估价回收',
            self::SOURCE_TYPE_DIRECT => '直接回收',
            self::SOURCE_TYPE_BATCH => '批量回收'
        ];
        return $sourceTypeTexts[$sourceType] ?? '未知来源';
    }

    /**
     * 获取配送方式文本
     * @param int $deliveryType
     * @return string
     */
    public static function getDeliveryTypeText($deliveryType)
    {
        $deliveryTypeTexts = [
            self::DELIVERY_TYPE_EXPRESS => '快递上门',
            self::DELIVERY_TYPE_SELF => '用户自寄'
        ];
        return $deliveryTypeTexts[$deliveryType] ?? '未知方式';
    }

    /**
     * 获取快递状态文本
     * @param int $expressStatus
     * @return string
     */
    public static function getExpressStatusText($expressStatus)
    {
        $expressStatusTexts = [
            self::EXPRESS_STATUS_NONE => '暂无轨迹',
            self::EXPRESS_STATUS_PICKED => '已揽收',
            self::EXPRESS_STATUS_TRANSIT => '运输中',
            self::EXPRESS_STATUS_DELIVERED => '已签收',
            self::EXPRESS_STATUS_EXCEPTION => '异常'
        ];
        return $expressStatusTexts[$expressStatus] ?? '未知状态';
    }

    /**
     * 关联会员信息
     * @return \think\model\relation\BelongsTo
     */
    public function member()
    {
        return $this->belongsTo('app\model\member\Member', 'member_id', 'member_id')
            ->field('member_id,nickname,mobile,headimg');
    }

    /**
     * 关联估价订单
     * @return \think\model\relation\BelongsTo
     */
    public function quoteOrder()
    {
        return $this->belongsTo('addon\yz_she\app\model\quote\QuoteOrder', 'quote_order_id', 'id');
    }

    /**
     * 关联商品分类
     * @return \think\model\relation\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo('addon\yz_she\app\model\goods\Category', 'category_id', 'id')
            ->field('id,category_name,image');
    }

    /**
     * 关联商品品牌
     * @return \think\model\relation\BelongsTo
     */
    public function brand()
    {
        return $this->belongsTo('addon\yz_she\app\model\goods\Brand', 'brand_id', 'id')
            ->field('id,brand_name,logo');
    }

    /**
     * 关联商品信息
     * @return \think\model\relation\BelongsTo
     */
    public function product()
    {
        return $this->belongsTo('addon\yz_she\app\model\goods\Goods', 'product_id', 'id')
            ->field('id,goods_name,goods_image,goods_code');
    }

    /**
     * 关联取件地址
     * @return \think\model\relation\BelongsTo
     */
    public function pickupAddress()
    {
        return $this->belongsTo('app\model\member\MemberAddress', 'pickup_address_id', 'id');
    }

    /**
     * 关联加价券
     * @return \think\model\relation\BelongsTo
     */
    public function voucher()
    {
        return $this->belongsTo('addon\yz_she\app\model\voucher\Voucher', 'voucher_id', 'id')
            ->field('id,voucher_no,amount,title');
    }

    /**
     * 关联订单日志
     * @return \think\model\relation\HasMany
     */
    public function logs()
    {
        return $this->hasMany('addon\yz_she\app\model\recycle\RecycleOrderLog', 'recycle_order_id', 'id')
            ->order('create_time desc');
    }

    /**
     * 搜索器：订单编号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchOrderNoAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('order_no', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：商品名称
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchProductNameAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('product_name', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：商品编码
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchProductCodeAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('product_code', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：发件人手机号
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchPickupContactPhoneAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('pickup_contact_phone', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器：用户关键词（昵称/手机号）
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchKeywordAttr($query, $value, $data)
    {
        if ($value) {
            $query->whereHas('member', function($q) use ($value) {
                $q->where('nickname', 'like', '%' . $value . '%')
                  ->whereOr('mobile', 'like', '%' . $value . '%');
            });
        }
    }

    /**
     * 搜索器：订单状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * 搜索器：订单来源
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchSourceTypeAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('source_type', $value);
        }
    }

    /**
     * 搜索器：配送方式
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchDeliveryTypeAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('delivery_type', $value);
        }
    }

    /**
     * 搜索器：创建时间
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchCreateTimeAttr($query, $value, $data)
    {
        if ($value && is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('create_time', $value[0], $value[1]);
        }
    }

    /**
     * 搜索器：结算时间
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchSettlementTimeAttr($query, $value, $data)
    {
        if ($value && is_array($value) && count($value) == 2) {
            $query->whereBetweenTime('settlement_time', $value[0], $value[1]);
        }
    }
}