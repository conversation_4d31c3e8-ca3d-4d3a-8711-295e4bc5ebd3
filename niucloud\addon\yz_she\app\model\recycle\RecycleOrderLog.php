<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\recycle;

use core\base\BaseModel;

/**
 * 回收订单日志模型
 * Class RecycleOrderLog
 * @package addon\yz_she\app\model\recycle
 */
class RecycleOrderLog extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_recycle_order_logs';

    /**
     * 自动时间戳
     * @var bool
     */
    protected $autoWriteTimestamp = true;

    /**
     * 创建时间字段
     * @var string
     */
    protected $createTime = 'create_time';

    /**
     * 更新时间字段
     * @var string|false
     */
    protected $updateTime = false;

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'recycle_order_id' => 'integer',
        'from_status' => 'integer',
        'to_status' => 'integer',
        'operator_id' => 'integer',
        'operator_type' => 'integer',
        'create_time' => 'timestamp'
    ];

    /**
     * 操作人类型
     */
    const OPERATOR_TYPE_USER = 1;       // 用户
    const OPERATOR_TYPE_ADMIN = 2;      // 管理员
    const OPERATOR_TYPE_SYSTEM = 3;     // 系统

    /**
     * 获取操作人类型文本
     * @param int $operatorType
     * @return string
     */
    public static function getOperatorTypeText($operatorType)
    {
        $operatorTypeTexts = [
            self::OPERATOR_TYPE_USER => '用户',
            self::OPERATOR_TYPE_ADMIN => '管理员',
            self::OPERATOR_TYPE_SYSTEM => '系统'
        ];
        return $operatorTypeTexts[$operatorType] ?? '未知';
    }

    /**
     * 关联回收订单
     * @return \think\model\relation\BelongsTo
     */
    public function recycleOrder()
    {
        return $this->belongsTo('addon\yz_she\app\model\recycle\RecycleOrder', 'recycle_order_id', 'id');
    }

    /**
     * 获取操作人名称修改器
     * @param $value
     * @param $data
     * @return string
     */
    public function getOperatorNameAttr($value, $data)
    {
        if ($data['operator_type'] == self::OPERATOR_TYPE_SYSTEM) {
            return '系统';
        }

        if ($data['operator_type'] == self::OPERATOR_TYPE_ADMIN && $data['operator_id']) {
            // 这里可以关联管理员表获取管理员名称
            return '管理员#' . $data['operator_id'];
        }

        if ($data['operator_type'] == self::OPERATOR_TYPE_USER && $data['operator_id']) {
            // 这里可以关联用户表获取用户名称
            return '用户#' . $data['operator_id'];
        }

        return '未知操作人';
    }

    /**
     * 获取原状态文本修改器
     * @param $value
     * @param $data
     * @return string
     */
    public function getFromStatusTextAttr($value, $data)
    {
        return $data['from_status'] ? RecycleOrder::getStatusText($data['from_status']) : '-';
    }

    /**
     * 获取新状态文本修改器
     * @param $value
     * @param $data
     * @return string
     */
    public function getToStatusTextAttr($value, $data)
    {
        return RecycleOrder::getStatusText($data['to_status']);
    }

    /**
     * 获取创建时间文本修改器
     * @param $value
     * @param $data
     * @return string
     */
    public function getCreateTimeTextAttr($value, $data)
    {
        return $data['create_time'] ? date('Y-m-d H:i:s', $data['create_time']) : '';
    }
}