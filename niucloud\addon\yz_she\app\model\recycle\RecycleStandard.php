<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的saas管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\model\recycle;

use core\base\BaseModel;

/**
 * 回收标准模型
 * Class RecycleStandard
 * @package addon\yz_she\app\model
 */
class RecycleStandard extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'yz_she_recycle_standards';

    /**
     * 关联分类
     * @return \think\model\relation\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(\addon\yz_she\app\model\goods\Category::class, 'category_id', 'id');
    }

    /**
     * 搜索器:回收标准分类
     * @param $value
     * @param $data
     */
    public function searchCategoryIdAttr($query, $value, $data)
    {
        if ($value != '' && $value != 0) {
            $query->where('category_id', $value);
        }
    }

    /**
     * 搜索器:回收标准标题
     * @param $value
     * @param $data
     */
    public function searchTitleAttr($query, $value, $data)
    {
        if ($value != '') {
            $query->where('title', 'like', '%' . $value . '%');
        }
    }

    /**
     * 搜索器:回收标准状态
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value != '') {
            $query->where('status', $value);
        }
    }
}
