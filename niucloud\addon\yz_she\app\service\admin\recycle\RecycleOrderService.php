<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\admin\recycle;

use addon\yz_she\app\model\recycle\RecycleOrder;
use addon\yz_she\app\model\recycle\RecycleOrderLog;
use core\base\BaseAdminService;
use think\facade\Db;

/**
 * 回收订单服务层
 * Class RecycleOrderService
 * @package addon\yz_she\app\service\admin\recycle
 */
class RecycleOrderService extends BaseAdminService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new RecycleOrder();
    }

    /**
     * 获取回收订单分页列表
     * @param array $where
     * @return array
     */
    public function getPage(array $where = [])
    {
        $field = 'id,order_no,quote_order_id,member_id,category_id,brand_id,product_id,product_name,product_code,product_image,pickup_address_id,voucher_id,expected_price,voucher_amount,final_price,total_amount,express_fee,status,source_type,delivery_type,settlement_status,express_status,express_company,express_number,pickup_contact_name,pickup_contact_phone,pickup_time,quantity,admin_note,create_time,update_time';
        $order = 'create_time desc';

        $search_model = $this->model
            ->withSearch(['order_no', 'product_name', 'product_code', 'pickup_contact_phone', 'keyword', 'status', 'source_type', 'delivery_type', 'create_time', 'settlement_time'], $where)
            ->with([
                'member' => function($query) {
                    $query->field('member_id,nickname,mobile,headimg');
                },
                'quoteOrder' => function($query) {
                    $query->field('id,order_no,quote_price');
                },
                'category' => function($query) {
                    $query->field('id,category_name,image');
                },
                'brand' => function($query) {
                    $query->field('id,brand_name,logo');
                },
                'product' => function($query) {
                    $query->field('id,goods_name,goods_image,goods_code');
                },
                'pickupAddress' => function($query) {
                    $query->field('id,contact_name,mobile,full_address');
                },
                'voucher' => function($query) {
                    $query->field('id,voucher_no,amount,title');
                }
            ])
            ->field($field)
            ->order($order);

        $list = $this->pageQuery($search_model);

        // 处理数据格式
        if (!empty($list['data'])) {
            foreach ($list['data'] as &$item) {
                $item = $this->formatOrderData($item);
            }
        }

        return $list;
    }

    /**
     * 获取回收订单详情
     * @param int $id
     * @return array
     */
    public function getInfo(int $id)
    {
        $field = '*';

        $info = $this->model
            ->with([
                'member' => function($query) {
                    $query->field('member_id,nickname,mobile,headimg,member_level');
                },
                'quoteOrder' => function($query) {
                    $query->field('id,order_no,quote_price,condition_score,quote_note');
                },
                'category' => function($query) {
                    $query->field('id,category_name,image');
                },
                'brand' => function($query) {
                    $query->field('id,brand_name,logo');
                },
                'product' => function($query) {
                    $query->field('id,goods_name,goods_image,goods_code');
                },
                'pickupAddress' => function($query) {
                    $query->field('id,contact_name,mobile,province_id,city_id,district_id,address,full_address,lng,lat');
                },
                'voucher' => function($query) {
                    $query->field('id,voucher_no,amount,title,expire_time');
                },
                'logs' => function($query) {
                    $query->field('id,from_status,to_status,operator_id,operator_type,change_reason,remark,create_time')
                          ->order('create_time desc');
                }
            ])
            ->field($field)
            ->findOrEmpty($id);

        if ($info->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        return $this->formatOrderData($info->toArray());
    }

    /**
     * 格式化订单数据
     * @param array $data
     * @return array
     */
    private function formatOrderData(array $data)
    {
        // 添加状态文本
        $data['status_text'] = RecycleOrder::getStatusText($data['status']);
        $data['source_type_text'] = RecycleOrder::getSourceTypeText($data['source_type']);
        $data['delivery_type_text'] = RecycleOrder::getDeliveryTypeText($data['delivery_type']);
        $data['express_status_text'] = RecycleOrder::getExpressStatusText($data['express_status']);

        // 格式化时间
        $data['create_time_text'] = $data['create_time'] ? date('Y-m-d H:i:s', $data['create_time']) : '';
        $data['pickup_time_text'] = $data['pickup_time'] ? date('Y-m-d H:i:s', $data['pickup_time']) : '';
        $data['receive_time_text'] = $data['receive_time'] ? date('Y-m-d H:i:s', $data['receive_time']) : '';
        $data['quality_start_time_text'] = $data['quality_start_time'] ? date('Y-m-d H:i:s', $data['quality_start_time']) : '';
        $data['quality_complete_time_text'] = $data['quality_complete_time'] ? date('Y-m-d H:i:s', $data['quality_complete_time']) : '';
        $data['settlement_time_text'] = $data['settlement_time'] ? date('Y-m-d H:i:s', $data['settlement_time']) : '';

        // 处理日志数据
        if (isset($data['logs']) && is_array($data['logs'])) {
            foreach ($data['logs'] as &$log) {
                $log['from_status_text'] = $log['from_status'] ? RecycleOrder::getStatusText($log['from_status']) : '-';
                $log['to_status_text'] = RecycleOrder::getStatusText($log['to_status']);
                $log['operator_name'] = $this->getOperatorName($log['operator_type'], $log['operator_id']);
                $log['create_time_text'] = $log['create_time'] ? date('Y-m-d H:i:s', $log['create_time']) : '';
            }
        }

        return $data;
    }

    /**
     * 获取操作人名称
     * @param int $operatorType
     * @param int $operatorId
     * @return string
     */
    private function getOperatorName($operatorType, $operatorId)
    {
        switch ($operatorType) {
            case RecycleOrderLog::OPERATOR_TYPE_SYSTEM:
                return '系统';
            case RecycleOrderLog::OPERATOR_TYPE_ADMIN:
                return '管理员#' . $operatorId;
            case RecycleOrderLog::OPERATOR_TYPE_USER:
                return '用户#' . $operatorId;
            default:
                return '未知操作人';
        }
    }

    /**
     * 获取状态统计数据
     * @return array
     */
    public function getStatusCounts()
    {
        $counts = $this->model
            ->field('status, count(*) as count')
            ->group('status')
            ->select()
            ->toArray();

        $result = [
            'pickup_pending' => 0,   // 待取件
            'receive_pending' => 0,  // 待收货
            'quality_pending' => 0,  // 待质检
            'confirm_pending' => 0,  // 待确认
            'return_pending' => 0,   // 待退回
            'returned' => 0,         // 已退回
            'completed' => 0         // 已完成
        ];

        foreach ($counts as $item) {
            switch ($item['status']) {
                case RecycleOrder::STATUS_PICKUP_PENDING:
                    $result['pickup_pending'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_RECEIVE_PENDING:
                    $result['receive_pending'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_QUALITY_PENDING:
                    $result['quality_pending'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_CONFIRM_PENDING:
                    $result['confirm_pending'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_RETURN_PENDING:
                    $result['return_pending'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_RETURNED:
                    $result['returned'] = $item['count'];
                    break;
                case RecycleOrder::STATUS_COMPLETED:
                    $result['completed'] = $item['count'];
                    break;
            }
        }

        return $result;
    }

    /**
     * 获取状态选项
     * @return array
     */
    public function getStatusOptions()
    {
        return [
            RecycleOrder::STATUS_PICKUP_PENDING => '待取件',
            RecycleOrder::STATUS_RECEIVE_PENDING => '待收货',
            RecycleOrder::STATUS_QUALITY_PENDING => '待质检',
            RecycleOrder::STATUS_CONFIRM_PENDING => '待确认',
            RecycleOrder::STATUS_RETURN_PENDING => '待退回',
            RecycleOrder::STATUS_RETURNED => '已退回',
            RecycleOrder::STATUS_COMPLETED => '已完成'
        ];
    }

    /**
     * 获取来源类型选项
     * @return array
     */
    public function getSourceTypeOptions()
    {
        return [
            RecycleOrder::SOURCE_TYPE_ESTIMATE => '估价回收',
            RecycleOrder::SOURCE_TYPE_DIRECT => '直接回收',
            RecycleOrder::SOURCE_TYPE_BATCH => '批量回收'
        ];
    }

    /**
     * 获取配送方式选项
     * @return array
     */
    public function getDeliveryTypeOptions()
    {
        return [
            RecycleOrder::DELIVERY_TYPE_EXPRESS => '快递上门',
            RecycleOrder::DELIVERY_TYPE_SELF => '用户自寄'
        ];
    }

    /**
     * 收货确认
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function receiveOrder(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        if ($order['status'] != RecycleOrder::STATUS_RECEIVE_PENDING) {
            throw new \Exception('订单状态不正确，无法收货');
        }

        Db::startTrans();
        try {
            // 更新订单状态
            $updateData = [
                'status' => RecycleOrder::STATUS_QUALITY_PENDING,
                'receive_time' => time(),
                'receive_note' => $data['receive_note'] ?? '',
                'receive_status' => $data['receive_status'] ?? 1
            ];

            $order->save($updateData);

            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_RECEIVE_PENDING, RecycleOrder::STATUS_QUALITY_PENDING,
                RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '收货确认', $data['receive_note'] ?? '');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 开始质检
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function startQualityCheck(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        if ($order['status'] != RecycleOrder::STATUS_QUALITY_PENDING) {
            throw new \Exception('订单状态不正确，无法开始质检');
        }

        Db::startTrans();
        try {
            // 更新订单状态
            $updateData = [
                'status' => RecycleOrder::STATUS_CONFIRM_PENDING,
                'quality_start_time' => time()
            ];

            $order->save($updateData);

            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_QUALITY_PENDING, RecycleOrder::STATUS_CONFIRM_PENDING,
                RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '开始质检');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 完成质检
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function completeQualityCheck(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        if ($order['status'] != RecycleOrder::STATUS_CONFIRM_PENDING) {
            throw new \Exception('订单状态不正确，无法完成质检');
        }

        Db::startTrans();
        try {
            // 更新订单状态
            $updateData = [
                'status' => RecycleOrder::STATUS_COMPLETED,
                'quality_complete_time' => time(),
                'quality_score' => $data['quality_score'] ?? 0,
                'final_price' => $data['final_price'] ?? 0,
                'quality_note' => $data['quality_note'] ?? '',
                'quality_images' => isset($data['quality_images']) ? json_encode($data['quality_images']) : ''
            ];

            // 计算总金额（最终价格 + 加价券金额）
            $updateData['total_amount'] = $updateData['final_price'] + ($order['voucher_amount'] ?? 0);

            $order->save($updateData);

            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_CONFIRM_PENDING, RecycleOrder::STATUS_COMPLETED,
                RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '完成质检', $data['quality_note'] ?? '');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 订单结算
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function settlementOrder(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        if ($order['status'] != RecycleOrder::STATUS_COMPLETED) {
            throw new \Exception('订单状态不正确，无法结算');
        }

        if ($order['settlement_status'] == RecycleOrder::SETTLEMENT_STATUS_COMPLETED) {
            throw new \Exception('订单已结算，无法重复结算');
        }

        Db::startTrans();
        try {
            // 更新订单结算状态
            $updateData = [
                'settlement_status' => RecycleOrder::SETTLEMENT_STATUS_COMPLETED,
                'settlement_time' => time(),
                'settlement_note' => $data['settlement_note'] ?? ''
            ];

            $order->save($updateData);

            // 给用户账户增加余额
            if ($order['total_amount'] > 0) {
                // 这里需要调用会员余额增加的服务
                // (new MemberAccountService())->addBalance($order['member_id'], $order['total_amount'], '回收订单结算', $order['order_no']);
            }

            // 记录日志
            $this->addOrderLog($id, $order['status'], $order['status'],
                RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '订单结算', $data['settlement_note'] ?? '');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 退回订单
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function returnOrder(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        if (!in_array($order['status'], [RecycleOrder::STATUS_CONFIRM_PENDING, RecycleOrder::STATUS_RETURN_PENDING])) {
            throw new \Exception('订单状态不正确，无法退回');
        }

        Db::startTrans();
        try {
            // 更新订单状态
            $updateData = [
                'status' => RecycleOrder::STATUS_RETURNED,
                'return_time' => time(),
                'return_note' => $data['return_note'] ?? ''
            ];

            $order->save($updateData);

            // 记录日志
            $this->addOrderLog($id, $order['status'], RecycleOrder::STATUS_RETURNED,
                RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '订单退回', $data['return_note'] ?? '');

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新快递信息
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateExpressInfo(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        Db::startTrans();
        try {
            // 更新快递信息
            $updateData = [
                'express_company' => $data['express_company'] ?? '',
                'express_number' => $data['express_number'] ?? '',
                'express_fee' => $data['express_fee'] ?? 0,
                'express_note' => $data['express_note'] ?? ''
            ];

            // 如果是第一次添加快递信息，更新状态为待收货
            if ($order['status'] == RecycleOrder::STATUS_PICKUP_PENDING && !empty($data['express_number'])) {
                $updateData['status'] = RecycleOrder::STATUS_RECEIVE_PENDING;
                $updateData['express_status'] = RecycleOrder::EXPRESS_STATUS_PICKED;
            }

            $order->save($updateData);

            // 记录日志
            if (isset($updateData['status'])) {
                $this->addOrderLog($id, RecycleOrder::STATUS_PICKUP_PENDING, RecycleOrder::STATUS_RECEIVE_PENDING,
                    RecycleOrderLog::OPERATOR_TYPE_ADMIN, $data['admin_id'] ?? 0, '更新快递信息', $data['express_note'] ?? '');
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新订单备注
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateOrderNotes(int $id, array $data)
    {
        $order = $this->model->findOrEmpty($id);
        if ($order->isEmpty()) {
            throw new \Exception('订单不存在');
        }

        $updateData = [
            'admin_note' => $data['admin_note'] ?? ''
        ];

        return $order->save($updateData) !== false;
    }

    /**
     * 获取订单日志
     * @param int $id
     * @return array
     */
    public function getOrderLogs(int $id)
    {
        $logs = (new RecycleOrderLog())
            ->where('recycle_order_id', $id)
            ->field('id,from_status,to_status,operator_id,operator_type,change_reason,remark,create_time')
            ->order('create_time desc')
            ->select()
            ->toArray();

        foreach ($logs as &$log) {
            $log['from_status_text'] = $log['from_status'] ? RecycleOrder::getStatusText($log['from_status']) : '-';
            $log['to_status_text'] = RecycleOrder::getStatusText($log['to_status']);
            $log['operator_name'] = $this->getOperatorName($log['operator_type'], $log['operator_id']);
            $log['create_time_text'] = $log['create_time'] ? date('Y-m-d H:i:s', $log['create_time']) : '';
        }

        return $logs;
    }

    /**
     * 批量操作
     * @param array $data
     * @return bool
     */
    public function batchOperation(array $data)
    {
        $ids = $data['ids'] ?? [];
        $action = $data['action'] ?? '';
        $adminId = $data['admin_id'] ?? 0;

        if (empty($ids) || empty($action)) {
            throw new \Exception('参数错误');
        }

        Db::startTrans();
        try {
            switch ($action) {
                case 'start_quality':
                    // 批量开始质检
                    foreach ($ids as $id) {
                        $this->startQualityCheck($id, ['admin_id' => $adminId]);
                    }
                    break;
                case 'settlement':
                    // 批量结算
                    foreach ($ids as $id) {
                        $this->settlementOrder($id, ['admin_id' => $adminId]);
                    }
                    break;
                default:
                    throw new \Exception('不支持的操作类型');
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 导出订单数据
     * @param array $where
     * @return array
     */
    public function exportData(array $where = [])
    {
        $field = 'id,order_no,member_id,product_name,expected_price,final_price,total_amount,status,source_type,delivery_type,settlement_status,create_time,settlement_time';

        $list = $this->model
            ->withSearch(['order_no', 'product_name', 'product_code', 'pickup_contact_phone', 'keyword', 'status', 'source_type', 'delivery_type', 'create_time', 'settlement_time'], $where)
            ->with([
                'member' => function($query) {
                    $query->field('member_id,nickname,mobile');
                }
            ])
            ->field($field)
            ->order('create_time desc')
            ->select()
            ->toArray();

        // 格式化导出数据
        $exportData = [];
        foreach ($list as $item) {
            $exportData[] = [
                '订单编号' => $item['order_no'],
                '用户昵称' => $item['member']['nickname'] ?? '',
                '用户手机' => $item['member']['mobile'] ?? '',
                '商品名称' => $item['product_name'],
                '预期价格' => $item['expected_price'],
                '最终价格' => $item['final_price'],
                '结算金额' => $item['total_amount'],
                '订单状态' => RecycleOrder::getStatusText($item['status']),
                '订单来源' => RecycleOrder::getSourceTypeText($item['source_type']),
                '配送方式' => RecycleOrder::getDeliveryTypeText($item['delivery_type']),
                '结算状态' => $item['settlement_status'] ? '已结算' : '未结算',
                '创建时间' => $item['create_time'] ? date('Y-m-d H:i:s', $item['create_time']) : '',
                '结算时间' => $item['settlement_time'] ? date('Y-m-d H:i:s', $item['settlement_time']) : ''
            ];
        }

        return $exportData;
    }

    /**
     * 添加订单日志
     * @param int $orderId
     * @param int $fromStatus
     * @param int $toStatus
     * @param int $operatorType
     * @param int $operatorId
     * @param string $changeReason
     * @param string $remark
     * @return bool
     */
    private function addOrderLog(int $orderId, int $fromStatus, int $toStatus, int $operatorType, int $operatorId, string $changeReason = '', string $remark = '')
    {
        $logData = [
            'recycle_order_id' => $orderId,
            'from_status' => $fromStatus,
            'to_status' => $toStatus,
            'operator_id' => $operatorId,
            'operator_type' => $operatorType,
            'change_reason' => $changeReason,
            'remark' => $remark,
            'create_time' => time()
        ];

        return (new RecycleOrderLog())->save($logData) !== false;
    }
}