<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\service\core\recycle;

use addon\yz_she\app\model\recycle\RecycleOrder;
use addon\yz_she\app\model\recycle\RecycleOrderLog;
use core\base\BaseCoreService;

/**
 * 回收订单核心服务层
 */
class CoreRecycleOrderService extends BaseCoreService
{
    public function __construct()
    {
        parent::__construct();
        $this->model = new RecycleOrder();
    }

    /**
     * 创建回收订单
     * @param array $data
     * @return array
     */
    public function create(array $data)
    {
        // 验证必填字段
        $this->validateCreateData($data);

        // 生成订单号
        $data['order_no'] = $this->generateOrderNo();

        // 设置默认状态
        $data['status'] = RecycleOrder::STATUS_PICKUP_PENDING;
        $data['settlement_status'] = 0;
        $data['express_status'] = RecycleOrder::EXPRESS_STATUS_NONE;

        // 设置创建时间和更新时间
        $data['create_time'] = time();
        $data['update_time'] = time();

        // 订单创建时，最终价格和结算金额应该为空，等质检完成后再设置
        $data['final_price'] = null;
        $data['total_amount'] = null;


        // 清理可能导致问题的时间字段
        $timeFields = ['receive_time', 'quality_start_time', 'quality_complete_time', 'return_time', 'settlement_time'];
        foreach ($timeFields as $field) {
            if (isset($data[$field]) && (empty($data[$field]) || $data[$field] === '')) {
                unset($data[$field]);
            }
        }

        // 调试日志：检查传入的数据
        \think\facade\Log::info('创建订单前的数据:', [
            'pickup_address_detail' => $data['pickup_address_detail'] ?? 'NOT_SET',
            'all_data_keys' => array_keys($data)
        ]);

        // 创建订单
        $order = $this->model->create($data);

        // 调试日志：检查创建后的数据
        \think\facade\Log::info('创建订单后的数据:', [
            'order_id' => $order->id,
            'pickup_address_detail' => $order->pickup_address_detail ?? 'NOT_SET'
        ]);

        if (!$order) {
            throw new \Exception('订单创建失败');
        }

        // 记录订单日志
        $this->addOrderLog($order->id, 0, RecycleOrder::STATUS_PICKUP_PENDING, '用户创建订单', 'member', $data['member_id']);

        return $order->toArray();
    }

    /**
     * 更新快递信息
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateExpressInfo(int $id, array $data)
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        // 更新快递信息
        $updateData = [
            'express_company' => $data['express_company'],
            'express_number' => $data['express_number'],
            'express_status' => RecycleOrder::EXPRESS_STATUS_PICKED
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $this->addOrderLog($id, $order->status, $order->status, '用户更新快递信息：' . $data['express_company'] . ' ' . $data['express_number'], 'member', $order->member_id);
        }

        return $result !== false;
    }

    /**
     * 收货确认
     * @param int $id
     * @return bool
     */
    public function receiveOrder(int $id)
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if ($order->status != RecycleOrder::STATUS_RECEIVE_PENDING) {
            throw new \Exception('当前状态不允许收货');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_QUALITY_PENDING,
            'receive_time' => time(),
            'express_status' => RecycleOrder::EXPRESS_STATUS_DELIVERED
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_RECEIVE_PENDING, RecycleOrder::STATUS_QUALITY_PENDING, '平台确认收货', 'admin', 0);
        }

        return $result !== false;
    }

    /**
     * 开始质检
     * @param int $id
     * @return bool
     */
    public function startQualityCheck(int $id)
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if ($order->status != RecycleOrder::STATUS_QUALITY_PENDING) {
            throw new \Exception('当前状态不允许开始质检');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_QUALITY_CHECKING,
            'quality_start_time' => time()
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_QUALITY_PENDING, RecycleOrder::STATUS_QUALITY_CHECKING, '开始质检', 'admin', 0);
        }

        return $result !== false;
    }

    /**
     * 完成质检
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function completeQualityCheck(int $id, array $data)
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if ($order->status != RecycleOrder::STATUS_QUALITY_CHECKING) {
            throw new \Exception('当前状态不允许完成质检');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_CONFIRM_PENDING,
            'final_price' => $data['final_price'],
            'total_amount' => $data['final_price'] + ($order->voucher_amount ?? 0),
            'quality_complete_time' => time(),
            'admin_note' => $data['admin_note'] ?? ''
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $note = '质检完成，最终价格：' . $data['final_price'];
            if (!empty($data['admin_note'])) {
                $note .= '，备注：' . $data['admin_note'];
            }
            $this->addOrderLog($id, RecycleOrder::STATUS_QUALITY_CHECKING, RecycleOrder::STATUS_CONFIRM_PENDING, $note, 'admin', 0);
        }

        return $result !== false;
    }

    /**
     * 结算订单
     * @param int $id
     * @return bool
     */
    public function settlementOrder(int $id)
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if ($order->status != RecycleOrder::STATUS_CONFIRM_PENDING) {
            throw new \Exception('当前状态不允许结算');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_COMPLETED,
            'settlement_status' => 1,
            'settlement_time' => time()
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $this->addOrderLog($id, RecycleOrder::STATUS_CONFIRM_PENDING, RecycleOrder::STATUS_COMPLETED, '用户确认价格，订单完成', 'member', $order->member_id);
        }

        return $result !== false;
    }

    /**
     * 退回订单
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function returnOrder(int $id, string $reason = '')
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if (!in_array($order->status, [RecycleOrder::STATUS_CONFIRM_PENDING, RecycleOrder::STATUS_QUALITY_CHECKING])) {
            throw new \Exception('当前状态不允许申请退回');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_RETURNED,
            'return_time' => time(),
            'admin_note' => $reason
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $note = '用户申请退回';
            if ($reason) {
                $note .= '，原因：' . $reason;
            }
            $this->addOrderLog($id, $order->status, RecycleOrder::STATUS_RETURNED, $note, 'member', $order->member_id);
        }

        return $result !== false;
    }

    /**
     * 取消订单
     * @param int $id
     * @param string $reason
     * @return bool
     */
    public function cancelOrder(int $id, string $reason = '')
    {
        $order = $this->model->find($id);
        if (!$order) {
            throw new \Exception('订单不存在');
        }

        if (!in_array($order->status, [RecycleOrder::STATUS_PICKUP_PENDING, RecycleOrder::STATUS_RECEIVE_PENDING])) {
            throw new \Exception('当前状态不允许取消订单');
        }

        $updateData = [
            'status' => RecycleOrder::STATUS_CANCELLED,
            'admin_note' => $reason
        ];

        $result = $order->save($updateData);

        if ($result) {
            // 记录日志
            $note = '用户取消订单';
            if ($reason) {
                $note .= '，原因：' . $reason;
            }
            $this->addOrderLog($id, $order->status, RecycleOrder::STATUS_CANCELLED, $note, 'member', $order->member_id);
        }

        return $result !== false;
    }

    /**
     * 验证创建订单数据
     * @param array &$data
     * @throws \Exception
     */
    private function validateCreateData(array &$data)
    {
        if (empty($data['member_id'])) {
            throw new \Exception('会员ID不能为空');
        }

        if (empty($data['category_id'])) {
            throw new \Exception('商品分类不能为空');
        }

        if (empty($data['product_name'])) {
            throw new \Exception('商品名称不能为空');
        }

        if (empty($data['expected_price']) || $data['expected_price'] <= 0) {
            throw new \Exception('预期价格必须大于0');
        }

        if (!in_array($data['source_type'], [1, 2, 3])) {
            throw new \Exception('订单来源类型无效');
        }

        if (!in_array($data['delivery_type'], [1, 2])) {
            throw new \Exception('配送方式无效');
        }

        // 快递上门需要地址信息
        if ($data['delivery_type'] == 1) {
            if (empty($data['pickup_address_id'])) {
                throw new \Exception('快递上门需要选择取件地址');
            }
            if (empty($data['pickup_contact_name'])) {
                throw new \Exception('联系人姓名不能为空');
            }
            if (empty($data['pickup_contact_phone'])) {
                throw new \Exception('联系人电话不能为空');
            }
        }

        // 确保数值字段是正确的类型
        $numericFields = ['member_id', 'category_id', 'brand_id', 'product_id', 'pickup_address_id', 'voucher_id', 'expected_price', 'voucher_amount', 'final_price', 'total_amount', 'express_fee', 'source_type', 'delivery_type', 'quantity'];
        foreach ($numericFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = (int)$data[$field];
            }
        }
    }

    /**
     * 生成订单号
     * @return string
     */
    private function generateOrderNo()
    {
        return 'RC' . date('YmdHis') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
    }

    /**
     * 添加订单日志
     * @param int $orderId
     * @param int $fromStatus
     * @param int $toStatus
     * @param string $note
     * @param string $operatorType
     * @param int $operatorId
     * @return bool
     */
    private function addOrderLog(int $orderId, int $fromStatus, int $toStatus, string $note, string $operatorType = 'system', int $operatorId = 0)
    {
        // 根据操作人类型设置正确的数值
        $operatorTypeMap = [
            'member' => 1,
            'admin' => 2,
            'system' => 3
        ];

        $logData = [
            'recycle_order_id' => $orderId,
            'from_status' => $fromStatus,
            'to_status' => $toStatus,
            'remark' => $note,
            'operator_type' => $operatorTypeMap[$operatorType] ?? 3,
            'operator_id' => $operatorId,
            'create_time' => time()
        ];

        return (new RecycleOrderLog())->save($logData) !== false;
    }
}
