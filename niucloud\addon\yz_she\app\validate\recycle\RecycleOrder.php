<?php
// +----------------------------------------------------------------------
// | Niucloud-admin 企业快速开发的多应用管理平台
// +----------------------------------------------------------------------
// | 官方网址：https://www.niucloud.com
// +----------------------------------------------------------------------
// | niucloud团队 版权所有 开源版本可自由商用
// +----------------------------------------------------------------------
// | Author: Niucloud Team
// +----------------------------------------------------------------------

namespace addon\yz_she\app\validate\recycle;

use core\base\BaseValidate;

/**
 * 回收订单验证器
 * Class RecycleOrder
 * @package addon\yz_she\app\validate\recycle
 */
class RecycleOrder extends BaseValidate
{
    protected $rule = [
        'order_no' => 'require|max:32',
        'member_id' => 'require|integer|gt:0',
        'category_id' => 'require|integer|gt:0',
        'brand_id' => 'integer|egt:0',
        'product_id' => 'integer|egt:0',
        'product_name' => 'max:200',
        'product_code' => 'max:100',
        'status' => 'require|integer|in:1,2,3,4,5,6,7',
        'source_type' => 'require|integer|in:1,2,3',
        'quantity' => 'require|integer|gt:0',
        'expected_price' => 'require|float|egt:0',
        'final_price' => 'float|egt:0',
        'voucher_amount' => 'float|egt:0',
        'total_amount' => 'float|egt:0',
        'delivery_type' => 'require|integer|in:1,2',
        'pickup_contact_name' => 'max:50',
        'pickup_contact_phone' => 'max:20',
        'express_company' => 'max:50',
        'express_number' => 'max:50',
        'express_fee' => 'float|egt:0',
        'quality_score' => 'integer|between:0,100',
        'settlement_status' => 'integer|in:0,1',
        'admin_note' => 'max:500',
        'user_note' => 'max:500',
        'receive_note' => 'max:500',
        'quality_note' => 'max:500',
        'return_note' => 'max:500',
        'settlement_note' => 'max:500'
    ];

    protected $message = [
        'order_no.require' => '订单编号不能为空',
        'order_no.max' => '订单编号长度不能超过32个字符',
        'member_id.require' => '会员ID不能为空',
        'member_id.integer' => '会员ID必须是整数',
        'member_id.gt' => '会员ID必须大于0',
        'category_id.require' => '分类ID不能为空',
        'category_id.integer' => '分类ID必须是整数',
        'category_id.gt' => '分类ID必须大于0',
        'status.require' => '订单状态不能为空',
        'status.integer' => '订单状态必须是整数',
        'status.in' => '订单状态值不正确',
        'source_type.require' => '订单来源不能为空',
        'source_type.integer' => '订单来源必须是整数',
        'source_type.in' => '订单来源值不正确',
        'quantity.require' => '回收数量不能为空',
        'quantity.integer' => '回收数量必须是整数',
        'quantity.gt' => '回收数量必须大于0',
        'expected_price.require' => '预期价格不能为空',
        'expected_price.float' => '预期价格必须是数字',
        'expected_price.egt' => '预期价格不能小于0',
        'delivery_type.require' => '配送方式不能为空',
        'delivery_type.integer' => '配送方式必须是整数',
        'delivery_type.in' => '配送方式值不正确',
        'quality_score.integer' => '质检评分必须是整数',
        'quality_score.between' => '质检评分必须在0-100之间',
        'settlement_status.integer' => '结算状态必须是整数',
        'settlement_status.in' => '结算状态值不正确'
    ];

    protected $scene = [
        'add' => ['order_no', 'member_id', 'category_id', 'brand_id', 'product_id', 'product_name', 'status', 'source_type', 'quantity', 'expected_price', 'delivery_type'],
        'edit' => ['order_no', 'member_id', 'category_id', 'brand_id', 'product_id', 'product_name', 'status', 'source_type', 'quantity', 'expected_price', 'delivery_type'],
        'receive' => ['receive_note'],
        'quality' => ['quality_score', 'final_price', 'quality_note'],
        'settlement' => ['settlement_note'],
        'return' => ['return_note'],
        'express' => ['express_company', 'express_number', 'express_fee'],
        'notes' => ['admin_note']
    ];
}