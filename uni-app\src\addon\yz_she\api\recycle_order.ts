import request from '@/utils/request'

// 回收订单接口参数类型
export interface RecycleOrderCreateParams {
  quote_order_id?: number
  category_id: number
  brand_id?: number
  product_id?: number
  product_name: string
  product_code?: string
  product_image?: string
  pickup_address_id: number
  voucher_id?: number
  expected_price: number
  voucher_amount?: number
  final_price: number
  total_amount: number
  express_fee?: number
  source_type: number // 1=估价回收, 2=直接回收, 3=批量回收
  delivery_type: number // 1=快递上门, 2=用户自寄
  pickup_contact_name: string
  pickup_contact_phone: string
  pickup_time?: string
  quantity?: number
  admin_note?: string
}

export interface RecycleOrderListParams {
  order_no?: string
  status?: number | string
  source_type?: number
  delivery_type?: number
  page?: number
  limit?: number
}

export interface UpdateExpressParams {
  express_company: string
  express_number: string
}

/**
 * 创建回收订单
 */
export function createRecycleOrder(params: RecycleOrderCreateParams) {
  return request.post('yz_she/recycle_order', params)
}

/**
 * 获取回收订单列表
 */
export function getRecycleOrderList(params: RecycleOrderListParams) {
  const queryParams: any = {}

  if (params.order_no && params.order_no.trim()) {
    queryParams.order_no = params.order_no.trim()
  }

  if (params.status !== undefined && params.status !== null && params.status !== '') {
    queryParams.status = Number(params.status)
  }

  if (params.source_type && params.source_type > 0) {
    queryParams.source_type = Number(params.source_type)
  }

  if (params.delivery_type && params.delivery_type > 0) {
    queryParams.delivery_type = Number(params.delivery_type)
  }

  if (params.page && params.page > 0) {
    queryParams.page = Number(params.page)
  }

  if (params.limit && params.limit > 0) {
    queryParams.limit = Number(params.limit)
  }

  return request.get('yz_she/recycle_order/list', queryParams)
}

/**
 * 获取回收订单详情
 */
export function getRecycleOrderDetail(id: number) {
  return request.get(`yz_she/recycle_order/${id}`)
}

/**
 * 更新快递信息
 */
export function updateExpressInfo(id: number, params: UpdateExpressParams) {
  return request.post(`yz_she/recycle_order/${id}/express`, params)
}

/**
 * 确认收货
 */
export function confirmReceive(id: number) {
  return request.post(`yz_she/recycle_order/${id}/receive`)
}

/**
 * 开始质检
 */
export function startQualityCheck(id: number) {
  return request.post(`yz_she/recycle_order/${id}/quality_start`)
}

/**
 * 完成质检
 */
export function completeQualityCheck(id: number, params: { final_price: number, admin_note?: string }) {
  return request.post(`yz_she/recycle_order/${id}/quality_complete`, params)
}

/**
 * 确认价格并结算
 */
export function confirmPriceAndSettle(id: number) {
  return request.post(`yz_she/recycle_order/${id}/settlement`)
}

/**
 * 申请退回
 */
export function requestReturn(id: number, reason?: string) {
  return request.post(`yz_she/recycle_order/${id}/return`, { reason })
}

/**
 * 取消订单
 */
export function cancelRecycleOrder(id: number, reason?: string) {
  return request.post(`yz_she/recycle_order/${id}/cancel`, { reason })
}

/**
 * 获取订单状态列表
 */
export function getRecycleOrderStatus() {
  return request.get('yz_she/recycle_order/status')
}

/**
 * 获取配送方式选项
 */
export function getDeliveryTypeOptions() {
  return request.get('yz_she/recycle_order/delivery_types')
}

/**
 * 获取订单来源选项
 */
export function getSourceTypeOptions() {
  return request.get('yz_she/recycle_order/source_types')
}
