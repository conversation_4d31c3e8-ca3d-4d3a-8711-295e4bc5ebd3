<template>
  <view class="batch-order-page">
    <!-- 三步换钱 -->
    <view class="process-section">
      <text class="section-title">三步换钱</text>
      <view class="process-steps">
        <view class="step-item">
          <view class="step-icon">📦</view>
          <text class="step-text">{{ selectedDelivery === 'pickup' ? '包邮寄出' : '自行寄出' }}</text>
        </view>
        <view class="step-arrow">></view>
        <view class="step-item">
          <view class="step-icon">📋</view>
          <text class="step-text">专业质检估价</text>
        </view>
        <view class="step-arrow">></view>
        <view class="step-item">
          <view class="step-icon">💰</view>
          <text class="step-text">确认回收秒到账</text>
        </view>
      </view>
    </view>

    <!-- 回收数量设置 -->
    <view class="quantity-section">
      <view class="quantity-header">
        <text class="quantity-title">回收数量</text>
        <text class="quantity-desc">最多可添加100件</text>
      </view>
      <view class="quantity-control">
        <view class="quantity-btn" @click="decreaseQuantity">
          <text class="btn-text">-</text>
        </view>
        <input 
          class="quantity-input" 
          v-model="quantity" 
          type="number" 
          :maxlength="3"
        />
        <view class="quantity-btn" @click="increaseQuantity">
          <text class="btn-text">+</text>
        </view>
      </view>
    </view>

    <!-- 配送方式 -->
    <view class="delivery-section">
      <view class="delivery-options">
        <view
          class="delivery-option"
          :class="{ active: selectedDelivery === 'pickup' }"
          @click="selectDelivery('pickup')"
        >
          <view class="option-content">
            <text class="option-name">快递上门</text>
            <view class="option-tag">
              <text class="tag-text">免费</text>
            </view>
          </view>
        </view>
        <view
          class="delivery-option"
          :class="{ active: selectedDelivery === 'self' }"
          @click="selectDelivery('self')"
        >
          <text class="option-name">自行寄出</text>
        </view>
      </view>

      <!-- 快递上门内容 -->
      <view v-if="selectedDelivery === 'pickup'" class="pickup-content">
        <!-- 地址选择 -->
        <view class="address-item" @click="selectAddress">
          <view class="address-icon">
            <svg viewBox="0 0 1024 1024" width="32" height="32">
              <path d="M512 85.333333c-164.949333 0-298.666667 133.717333-298.666667 298.666667 0 164.949333 298.666667 554.666667 298.666667 554.666667s298.666667-389.717333 298.666667-554.666667c0-164.949333-133.717333-298.666667-298.666667-298.666667z m0 405.333334c-58.88 0-106.666667-47.786667-106.666667-106.666667s47.786667-106.666667 106.666667-106.666667 106.666667 47.786667 106.666667 106.666667-47.786667 106.666667-106.666667 106.666667z" fill="#0d7377"/>
            </svg>
          </view>
          <view class="address-content">
            <text class="address-text" v-if="!selectedAddress">请选择取件地址</text>
            <view v-else class="selected-address">
              <text class="address-name">{{ selectedAddress.name }} {{ selectedAddress.mobile }}</text>
              <text class="address-detail">{{ selectedAddress.full_address }}</text>
            </view>
          </view>
          <view class="divider-line"></view>
          <text class="address-action">地址簿</text>
        </view>

        <!-- 预约时间 -->
        <view class="time-item" :class="{ disabled: !selectedAddress }" @click="showTimeModal">
          <view class="time-icon">
            <svg viewBox="0 0 1024 1024" width="32" height="32">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m176.5 585.7l-28.6 39c-4.4 6.1-11.9 9.3-19.4 9.3-4.7 0-9.5-1.4-13.6-4.3L512 609.7l-114.9 84c-9.9 7.2-23.8 5.1-31-4.9l-28.6-39c-7.2-9.9-5.1-23.8 4.9-31L457 515.3V264c0-11 9-20 20-20h70c11 0 20 9 20 20v251.3l114.6 103.5c9.9 7.1 12 21 4.9 30.9z" :fill="selectedAddress ? '#0d7377' : '#ccc'"/>
            </svg>
          </view>
          <text class="time-text">期望上门时间</text>
          <text class="time-action" :class="{ disabled: !selectedAddress }">
            {{ !selectedAddress ? '请先选择地址' : (selectedTime || '尽快上门') }} >
          </text>
        </view>
      </view>

      <!-- 自行寄出内容 -->
      <view v-if="selectedDelivery === 'self'" class="self-content">
        <!-- 收货地址 -->
        <view class="address-item">
          <view class="address-icon orange-bg">
            <text class="address-text-icon">收</text>
          </view>
          <view class="address-info">
            <text class="address-name">放心星仓库 13060000687</text>
            <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
          </view>
          <text class="copy-btn">复制</text>
        </view>

        <!-- 快递公司 -->
        <view class="express-item" @click="showExpressModal">
          <view class="express-icon">
            <u-icon name="car" color="#0d7377" size="32"></u-icon>
          </view>
          <text class="express-text">快递公司</text>
          <text class="express-action">{{ selectedExpress || '请选择快递公司' }} ></text>
        </view>

        <!-- 快递单号 -->
        <view class="tracking-item">
          <view class="tracking-icon">
            <u-icon name="order" color="#0d7377" size="32"></u-icon>
          </view>
          <text class="tracking-text">快递单号</text>
          <input
            class="tracking-input"
            v-model="trackingNumber"
            placeholder="请输入快递单号"
            maxlength="30"
          />
        </view>
      </view>
    </view>

    <!-- 时间选择弹窗 -->
    <view class="time-modal" v-if="showTime" @click="hideTimeModal">
      <view class="time-modal-content" @click.stop>
        <view class="time-header">
          <text class="time-title">期望上门时间</text>
          <view class="close-btn" @click="hideTimeModal">×</view>
        </view>

        <!-- 左右分栏布局 -->
        <view class="time-container">
          <!-- 左侧日期选择 -->
          <view class="date-sidebar">
            <view
              class="date-item"
              :class="{ active: selectedDate === 'today' }"
              @click="selectDate('today')"
            >
              <text class="date-name">今天</text>
              <text class="date-desc">{{ todayDesc }}</text>
            </view>
            <view
              class="date-item"
              :class="{ active: selectedDate === 'tomorrow' }"
              @click="selectDate('tomorrow')"
            >
              <text class="date-name">明天</text>
              <text class="date-desc">{{ tomorrowDesc }}</text>
            </view>
            <view
              class="date-item"
              :class="{ active: selectedDate === 'dayafter' }"
              @click="selectDate('dayafter')"
            >
              <text class="date-name">后天</text>
              <text class="date-desc">{{ dayafterDesc }}</text>
            </view>
          </view>

          <!-- 右侧时间选择 -->
          <view class="time-content">
            <!-- 今天的时间选项 -->
            <view v-if="selectedDate === 'today'" class="time-slots">
              <view
                class="time-slot urgent"
                :class="{ active: selectedTimeSlot === 'today-urgent' }"
                @click="selectTimeSlot('today-urgent')"
              >
                <text class="slot-title">尽快上门</text>
                <text class="slot-desc">工作日2小时内</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-afternoon' }"
                @click="selectTimeSlot('today-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'today-evening' }"
                @click="selectTimeSlot('today-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>

            <!-- 明天的时间选项 -->
            <view v-if="selectedDate === 'tomorrow'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-morning' }"
                @click="selectTimeSlot('tomorrow-morning')"
              >
                <text class="slot-title">09:00-11:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-noon' }"
                @click="selectTimeSlot('tomorrow-noon')"
              >
                <text class="slot-title">11:00-13:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-afternoon' }"
                @click="selectTimeSlot('tomorrow-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'tomorrow-evening' }"
                @click="selectTimeSlot('tomorrow-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>

            <!-- 后天的时间选项 -->
            <view v-if="selectedDate === 'dayafter'" class="time-slots">
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-morning' }"
                @click="selectTimeSlot('dayafter-morning')"
              >
                <text class="slot-title">09:00-11:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-noon' }"
                @click="selectTimeSlot('dayafter-noon')"
              >
                <text class="slot-title">11:00-13:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-afternoon' }"
                @click="selectTimeSlot('dayafter-afternoon')"
              >
                <text class="slot-title">14:00-16:00</text>
              </view>
              <view
                class="time-slot"
                :class="{ active: selectedTimeSlot === 'dayafter-evening' }"
                @click="selectTimeSlot('dayafter-evening')"
              >
                <text class="slot-title">16:00-18:00</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <view class="express-modal" v-if="showExpress" @click="hideExpressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择快递公司</text>
          <view class="close-btn" @click="hideExpressModal">×</view>
        </view>

        <view class="express-list">
          <view
            class="express-option"
            v-for="express in expressList"
            :key="express"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view class="express-check" v-if="selectedExpress === express">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部确认按钮 -->
    <view class="bottom-action">
      <view class="confirm-button" @click="confirmBatchOrder">
        <text class="confirm-text">确认回收</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onShow as onPageShow, onLoad } from '@dcloudio/uni-app'
import { createRecycleOrder } from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 响应式数据
const quantity = ref(1)
const categoryId = ref<number | null>(null) // 分类ID
const selectedDelivery = ref('pickup') // pickup: 快递上门, self: 自行寄出
const selectedAddress = ref(null) // 选中的地址
const submitting = ref(false) // 提交状态
const selectedTime = ref('尽快上门') // 选中的时间
const selectedExpress = ref('') // 选中的快递公司
const trackingNumber = ref('') // 快递单号
const showTime = ref(false) // 控制时间选择弹窗
const showExpress = ref(false) // 控制快递公司弹窗
const selectedDate = ref('today') // 选中的日期
const selectedTimeSlot = ref('today-urgent') // 选中的时间段

// 日期描述
const todayDesc = ref('')
const tomorrowDesc = ref('')
const dayafterDesc = ref('')

// 快递公司列表
const expressList = [
  '顺丰速运',
  '京东物流',
  '德邦快递',
  '中通快递',
  '韵达速递',
  '圆通速递',
  '申通快递',
  '极兔速递'
]

// 数量控制方法
const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const increaseQuantity = () => {
  if (quantity.value < 100) {
    quantity.value++
  }
}

// 配送方式选择
const selectDelivery = (type: string) => {
  selectedDelivery.value = type
  console.log('选择配送方式:', type)
}

// 地址选择
const selectAddress = () => {
  // 设置地址选择回调
  uni.setStorage({
    key: 'selectAddressCallback',
    data: {
      back: '/addon/yz_she/pages/evaluate/batch'
    },
    success() {
      uni.navigateTo({
        url: '/addon/yz_she/pages/address/index'
      })
    }
  })
}

// 时间选项映射
const timeSlotMap = {
  'today-urgent': '尽快上门',
  'today-afternoon': '今天 14:00-16:00',
  'today-evening': '今天 16:00-18:00',
  'tomorrow-morning': '明天 09:00-11:00',
  'tomorrow-noon': '明天 11:00-13:00',
  'tomorrow-afternoon': '明天 14:00-16:00',
  'tomorrow-evening': '明天 16:00-18:00',
  'dayafter-morning': '后天 09:00-11:00',
  'dayafter-noon': '后天 11:00-13:00',
  'dayafter-afternoon': '后天 14:00-16:00',
  'dayafter-evening': '后天 16:00-18:00'
}

// 生成日期描述
const generateDateDescriptions = () => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  const dayafter = new Date(today)
  dayafter.setDate(today.getDate() + 2)

  todayDesc.value = `${today.getMonth() + 1}月${today.getDate()}日`
  tomorrowDesc.value = `${tomorrow.getMonth() + 1}月${tomorrow.getDate()}日`
  dayafterDesc.value = `${dayafter.getMonth() + 1}月${dayafter.getDate()}日`
}

// 时间选择相关方法
const showTimeModal = () => {
  // 检查是否已选择地址
  if (!selectedAddress.value) {
    uni.showToast({
      title: '请先选择取件地址',
      icon: 'none',
      duration: 2000
    })
    return
  }

  showTime.value = true
}

const hideTimeModal = () => {
  showTime.value = false
}

const selectDate = (date: string) => {
  selectedDate.value = date
}

const selectTimeSlot = (slot: string) => {
  selectedTimeSlot.value = slot
  const timeText = timeSlotMap[slot] || slot
  selectedTime.value = timeText
  showTime.value = false
  console.log('选择时间:', timeText)
}

// 快递公司选择
const showExpressModal = () => {
  showExpress.value = true
}

const hideExpressModal = () => {
  showExpress.value = false
}

const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpress.value = false
  console.log('选择快递公司:', express)
}

// 页面加载时获取参数
onLoad((options) => {
  console.log('批量回收页面接收到的参数:', options)
  pageParams.value = options || {}

  // 获取分类ID
  if (options.category_id) {
    categoryId.value = parseInt(options.category_id)
    console.log('获取到分类ID:', categoryId.value)
  }

  // 初始化日期描述
  generateDateDescriptions()
})





// 确认批量下单
const confirmBatchOrder = async () => {
  console.log('开始提交批量回收订单')

  // 防止重复提交
  if (submitting.value) {
    return
  }

  try {
    // 验证必填信息
    if (!validateOrderData()) {
      return
    }

    submitting.value = true

    // 构建订单数据
    const orderData = buildOrderData()
    console.log('批量订单数据:', orderData)

    // 调用API创建回收订单
    const response = await createRecycleOrder(orderData)

    if (response.code === 1) {
      console.log('批量回收订单创建成功:', response.data)

      uni.showToast({
        title: '订单提交成功',
        icon: 'success',
        duration: 1000
      })

      // 延迟跳转到成功页面
      setTimeout(() => {
        uni.redirectTo({
          url: `/addon/yz_she/pages/order/detail/batch-success?recycleOrderId=${response.data.id}`
        })
      }, 1000)
    } else {
      throw new Error(response.msg || '订单提交失败')
    }

  } catch (error) {
    console.error('提交批量订单失败:', error)
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
      duration: 3000
    })
  } finally {
    submitting.value = false
  }
}

// 验证订单数据
const validateOrderData = () => {
  if (quantity.value < 1) {
    uni.showToast({
      title: '请选择商品数量',
      icon: 'none'
    })
    return false
  }

  if (selectedDelivery.value === 'pickup') {
    if (!selectedAddress.value) {
      uni.showToast({
        title: '请选择取件地址',
        icon: 'none'
      })
      return false
    }
  } else if (selectedDelivery.value === 'self') {
    if (!selectedExpress.value) {
      uni.showToast({
        title: '请选择快递公司',
        icon: 'none'
      })
      return false
    }
    if (!trackingNumber.value) {
      uni.showToast({
        title: '请输入快递单号',
        icon: 'none'
      })
      return false
    }
  }

  return true
}

// 构建订单数据
const buildOrderData = () => {
  const orderData = {
    // 基础商品信息（批量回收使用默认商品信息）
    category_id: categoryId.value || 1, // 使用传入的分类ID，默认为1
    brand_id: null,
    product_id: null,
    product_name: '批量回收商品',
    product_code: '', // 商品编码为空，由后端生成
    product_image: '',

    // 价格信息
    expected_price: quantity.value * 100, // 预估回收价格，每件100元作为基础价格
    voucher_amount: 0,

    // 订单类型
    source_type: 2, // 2=直接回收
    delivery_type: selectedDelivery.value === 'pickup' ? 1 : 2, // 1=快递上门, 2=用户自寄

    // 数量 - 与步进器数量一致
    quantity: quantity.value,

    // 加价券信息
    voucher_id: null,

    // 估价订单关联
    quote_order_id: null
  }

  // 快递上门的信息
  if (selectedDelivery.value === 'pickup' && selectedAddress.value) {
    orderData.pickup_address_id = selectedAddress.value.id
    orderData.pickup_contact_name = selectedAddress.value.name
    orderData.pickup_contact_phone = selectedAddress.value.mobile
    orderData.pickup_address_detail = selectedAddress.value.full_address || selectedAddress.value.address_detail || ''
    orderData.pickup_time = selectedTime.value || '尽快上门'
  }

  // 用户自寄的信息
  if (selectedDelivery.value === 'self') {
    // 使用默认的收货地址信息
    orderData.pickup_contact_name = '放心星仓库'
    orderData.pickup_contact_phone = '13060000687'
    orderData.admin_note = `用户自寄，快递公司：${selectedExpress.value}，快递单号：${trackingNumber.value}`

    // 添加快递信息字段
    orderData.express_company = selectedExpress.value
    orderData.express_number = trackingNumber.value

    // 自行寄出直接设置为待收货状态
    orderData.status = 2 // 2=待收货
  }

  return orderData
}

// 页面显示时检查地址回调
const onShow = () => {
  // 检查地址选择回调
  const selectAddressCallback = uni.getStorageSync('selectAddressCallback')
  if (selectAddressCallback && selectAddressCallback.address_id) {
    // 使用回调中的地址信息
    if (selectAddressCallback.address_info) {
      selectedAddress.value = selectAddressCallback.address_info
    }

    // 清除回调数据
    uni.removeStorage({ key: 'selectAddressCallback' })
  }
}

// 添加页面生命周期
onPageShow(() => {
  onShow()
})

// 初始化
const init = () => {
  generateDateDescriptions()
  selectedTime.value = timeSlotMap[selectedTimeSlot.value] || '尽快上门'
}

// 监听页面显示
onPageShow(() => {
  onShow()
})
</script>

<style lang="scss" scoped>
.batch-order-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 120rpx;
}

// 三步换钱
.process-section {
  background-color: #fff;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .process-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .step-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;

      .step-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #f0f0f0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
      }

      .step-text {
        font-size: 22rpx;
        color: #666;
        text-align: center;
      }
    }

    .step-arrow {
      font-size: 24rpx;
      color: #ccc;
      margin: 0 16rpx;
    }
  }
}

// 回收数量设置
.quantity-section {
  background-color: #fff;
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .quantity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .quantity-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
    }

    .quantity-desc {
      font-size: 24rpx;
      color: #999;
    }
  }

  .quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40rpx;

    .quantity-btn {
      width: 60rpx;
      height: 60rpx;
      border: 2rpx solid #e9ecef;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      transition: all 0.3s ease;

      &:active {
        background-color: #f8f9fa;
        transform: scale(0.95);
      }

      .btn-text {
        font-size: 32rpx;
        color: #666;
        font-weight: 600;
      }
    }

    .quantity-input {
      width: 120rpx;
      height: 60rpx;
      text-align: center;
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
      border: 2rpx solid #e9ecef;
      border-radius: 12rpx;
      background-color: #fff;
    }
  }
}

// 配送方式
.delivery-section {
  background-color: #fff;
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;

  .delivery-options {
    display: flex;
    background-color: #f8f9fa;
    margin: 24rpx;
    border-radius: 12rpx;
    padding: 6rpx;

    .delivery-option {
      flex: 1;
      position: relative;
      padding: 16rpx 20rpx;
      background-color: transparent;
      border-radius: 8rpx;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      &:active {
        transform: scale(0.98);
      }

      &.active {
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .option-name {
          color: #333;
          font-weight: 600;
        }

        .option-tag {
          background-color: #16a085;
          box-shadow: 0 1rpx 4rpx rgba(22, 160, 133, 0.3);
        }
      }

      .option-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
      }

      .option-name {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .option-tag {
        background-color: #16a085;
        padding: 2rpx 8rpx;
        border-radius: 8rpx;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        .tag-text {
          font-size: 20rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }
  }

  .pickup-content, .self-content {
    border-top: 1rpx solid #f0f0f0;
  }

  .address-item, .time-item, .express-item, .tracking-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 1rpx solid #e9ecef;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f9fa;
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;

      &:active {
        background-color: transparent;
      }
    }

    .address-icon, .time-icon, .express-icon, .tracking-icon {
      margin-right: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.orange-bg {
        background-color: #16a085;
        border-radius: 50%;
        width: 48rpx;
        height: 48rpx;

        .address-text-icon {
          color: #fff;
          font-size: 24rpx;
          font-weight: 600;
          padding: 0 4rpx;
        }
      }
    }

    .address-content {
      flex: 1;

      .address-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }

      .selected-address {
        .address-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 600;
          margin-bottom: 8rpx;
          display: block;
        }

        .address-detail {
          font-size: 24rpx;
          color: #666;
          line-height: 1.4;
          display: block;
        }
      }
    }

    .divider-line {
      width: 2rpx;
      height: 32rpx;
      background-color: #e9ecef;
      margin: 0 16rpx;
    }

    .address-action, .time-action, .express-action {
      font-size: 26rpx;
      color: #6c757d;
      font-weight: 500;

      &.disabled {
        color: #999;
      }
    }

    .time-text, .express-text, .tracking-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .address-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .address-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
      }

      .address-detail {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .copy-btn {
      padding: 12rpx 20rpx;
      background-color: #f0f0f0;
      color: #666;
      font-size: 24rpx;
      border-radius: 20rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        background-color: #e0e0e0;
        transform: scale(0.95);
      }
    }

    .tracking-input {
      flex: 1;
      font-size: 26rpx;
      color: #333;
      text-align: right;
      background-color: transparent;
      border: none;
      outline: none;

      &::placeholder {
        color: #999;
      }
    }
  }
}

// 内容区域
.content-section {
  margin: 0 32rpx;

  .pickup-content, .self-content {
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  }

  .service-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.3s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f9fa;
    }

    .service-icon {
      margin-right: 24rpx;

      .icon-circle {
        width: 48rpx;
        height: 48rpx;
        background-color: #ff6b35;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 600;
      }
    }

    .service-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .service-action {
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
    }
  }

  .address-info {
    display: flex;
    align-items: flex-start;
    padding: 32rpx 24rpx;
    gap: 20rpx;

    .address-icon {
      .address-text-icon {
        width: 48rpx;
        height: 48rpx;
        background-color: #52c41a;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 600;
      }
    }

    .address-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .address-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
      }

      .address-detail {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
      }
    }

    .copy-btn {
      padding: 12rpx 24rpx;
      background-color: #f0f0f0;
      color: #666;
      font-size: 24rpx;
      border-radius: 20rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        background-color: #e0e0e0;
        transform: scale(0.95);
      }
    }
  }
}

// 底部操作区域
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .confirm-button {
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    color: #fff;
    padding: 18rpx 24rpx;
    border-radius: 50rpx;
    text-align: center;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.25);
    position: relative;
    overflow: hidden;

    // 光泽效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.6s ease;
    }

    &:active {
      transform: scale(0.98) translateY(1rpx);
      background: linear-gradient(135deg, #e55a2b 0%, #ff7a35 100%);
      box-shadow: 0 2rpx 12rpx rgba(255, 107, 53, 0.35);

      &::before {
        left: 100%;
      }
    }

    .confirm-text {
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
      letter-spacing: 1rpx;
    }
  }
}

// 时间选择弹窗样式
.time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .time-modal-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    max-height: 70vh;
    overflow: hidden;

    .time-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .time-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        background-color: #f5f5f5;
        border-radius: 50%;
      }
    }

    .time-container {
      display: flex;
      height: 500rpx;

      // 左侧日期选择
      .date-sidebar {
        width: 200rpx;
        background-color: #f8f9fa;
        border-right: 1rpx solid #e9ecef;

        .date-item {
          padding: 32rpx 24rpx;
          border-bottom: 1rpx solid #e9ecef;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;

          &.active {
            background-color: #fff;
            border-right: 4rpx solid #16a085;

            .date-name {
              color: #16a085;
              font-weight: 600;
            }

            .date-desc {
              color: #16a085;
            }

            &::before {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 0;
              height: 0;
              border-left: 8rpx solid #ff6b35;
              border-top: 8rpx solid transparent;
              border-bottom: 8rpx solid transparent;
            }
          }

          &:active {
            transform: scale(0.98);
          }

          .date-name {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            display: block;
            margin-bottom: 8rpx;
          }

          .date-desc {
            font-size: 24rpx;
            color: #666;
            display: block;
          }
        }
      }

      // 右侧时间选择
      .time-content {
        flex: 1;
        padding: 32rpx 24rpx;
        overflow-y: auto;

        .time-slots {
          display: flex;
          flex-direction: column;
          gap: 20rpx;

          .time-slot {
            padding: 24rpx 20rpx;
            border: 2rpx solid #e9ecef;
            border-radius: 12rpx;
            background-color: #fff;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;

            &.urgent {
              background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
              border-color: #16a085;

              .slot-title {
                color: #16a085;
                font-weight: 600;
              }

              .slot-desc {
                color: #16a085;
              }

              &::after {
                content: '推荐';
                position: absolute;
                top: -8rpx;
                right: 16rpx;
                background-color: #16a085;
                color: #fff;
                font-size: 20rpx;
                padding: 4rpx 12rpx;
                border-radius: 12rpx;
              }
            }

            &.active {
              border-color: #16a085;
              background-color: #f0fffe;
              box-shadow: 0 4rpx 16rpx rgba(22, 160, 133, 0.15);

              .slot-title {
                color: #16a085;
                font-weight: 600;
              }

              &.urgent {
                background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);

                .slot-title, .slot-desc {
                  color: #fff;
                }

                &::after {
                  background-color: #fff;
                  color: #16a085;
                }
              }
            }

            &:active {
              transform: scale(0.98);
            }

            .slot-title {
              font-size: 28rpx;
              color: #333;
              font-weight: 500;
              display: block;
              margin-bottom: 4rpx;
            }

            .slot-desc {
              font-size: 24rpx;
              color: #666;
              display: block;
            }
          }
        }
      }
    }
  }
}

// 快递公司选择弹窗
.express-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .modal-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    width: 100%;
    max-height: 60vh;
    overflow: hidden;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .express-list {
      padding: 24rpx 0;
      max-height: 400rpx;
      overflow-y: auto;

      .express-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: #f8f9fa;
        }

        .express-name {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }

        .express-check {
          font-size: 32rpx;
          color: #16a085;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
