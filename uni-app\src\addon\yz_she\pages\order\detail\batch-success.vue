<template>
  <view class="batch-success-page">
    <!-- 批量回收信息卡片 -->
    <view class="product-card">
      <view class="batch-info">
        <view class="batch-header">
          <view class="batch-icon">
            <u-icon name="shopping-cart-fill" color="#16a085" size="48"></u-icon>
          </view>
          <view class="batch-details">
            <text class="batch-title">批量回收</text>
            <text class="batch-status">提交成功</text>
          </view>
          <view class="quantity-display">
            <text class="quantity-number">{{ batchInfo.quantity }}</text>
            <text class="quantity-unit">件</text>
          </view>
        </view>

        <view class="delivery-info">
          <view class="delivery-item">
            <text class="delivery-label">配送方式</text>
            <text class="delivery-value">{{ batchInfo.deliveryText }}</text>
          </view>
          <!-- 上门回收显示详细地址信息 -->
          <template v-if="batchInfo.delivery === 'pickup'">
            <view class="delivery-item" v-if="recycleOrderInfo.pickup_contact_name || recycleOrderInfo.pickupAddress?.name">
              <text class="delivery-label">联系人</text>
              <text class="delivery-value">{{ recycleOrderInfo.pickup_contact_name || recycleOrderInfo.pickupAddress?.name }}</text>
            </view>
            <view class="delivery-item" v-if="recycleOrderInfo.pickup_contact_phone || recycleOrderInfo.pickupAddress?.mobile">
              <text class="delivery-label">联系电话</text>
              <text class="delivery-value">{{ recycleOrderInfo.pickup_contact_phone || recycleOrderInfo.pickupAddress?.mobile }}</text>
            </view>
            <view class="delivery-item" v-if="getFullAddress()">
              <text class="delivery-label">详细地址</text>
              <text class="delivery-value address-detail">{{ getFullAddress() }}</text>
            </view>
            <!-- 显示地址组成部分 - 暂时隐藏，因为只有ID没有名称 -->
            <!-- <view class="address-breakdown" v-if="hasAddressBreakdown()">
              <view class="delivery-item" v-if="recycleOrderInfo.pickup_province">
                <text class="delivery-label">省份</text>
                <text class="delivery-value">{{ recycleOrderInfo.pickup_province }}</text>
              </view>
              <view class="delivery-item" v-if="recycleOrderInfo.pickup_city">
                <text class="delivery-label">城市</text>
                <text class="delivery-value">{{ recycleOrderInfo.pickup_city }}</text>
              </view>
              <view class="delivery-item" v-if="recycleOrderInfo.pickup_district">
                <text class="delivery-label">区县</text>
                <text class="delivery-value">{{ recycleOrderInfo.pickup_district }}</text>
              </view>
            </view> -->
            <view class="delivery-item" v-if="batchInfo.time || recycleOrderInfo.pickup_time">
              <text class="delivery-label">预约时间</text>
              <text class="delivery-value">{{ batchInfo.time || recycleOrderInfo.pickup_time }}</text>
            </view>
            <!-- 显示订单状态 -->
            <view class="delivery-item" v-if="recycleOrderInfo.status_text">
              <text class="delivery-label">订单状态</text>
              <text class="delivery-value status-text">{{ recycleOrderInfo.status_text }}</text>
            </view>
          </template>

          <!-- 用户自寄显示物流信息 -->
          <template v-if="batchInfo.delivery === 'self'">
            <view class="delivery-item" v-if="recycleOrderInfo.express_company">
              <text class="delivery-label">物流公司</text>
              <text class="delivery-value">{{ recycleOrderInfo.express_company }}</text>
            </view>
            <view class="delivery-item" v-if="recycleOrderInfo.express_number">
              <text class="delivery-label">快递单号</text>
              <text class="delivery-value">{{ recycleOrderInfo.express_number }}</text>
            </view>
            <!-- 显示订单状态 -->
            <view class="delivery-item" v-if="recycleOrderInfo.status_text">
              <text class="delivery-label">订单状态</text>
              <text class="delivery-value status-text">{{ recycleOrderInfo.status_text }}</text>
            </view>
          </template>
        </view>
      </view>


    </view>

    <!-- 成功状态区域 -->
    <view class="success-section">
      <view class="success-icon">
        <view class="batch-bg">
          <view class="batch-body">
            <view class="batch-items">
              <view class="item-stack">
                <view class="stack-item"></view>
                <view class="stack-item"></view>
                <view class="stack-item"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="check-mark">
          <u-icon name="checkmark" color="#fff" size="16"></u-icon>
        </view>
      </view>

      <text class="success-title">批量回收提交成功</text>
      <text class="order-number">回收单号: {{ orderInfo.orderNo }}</text>
      <text class="success-desc">您的{{ batchInfo.quantity }}件商品回收申请已提交，我们将尽快安排处理</text>

      <!-- 查看订单按钮 -->
      <view class="view-order-button" @click="viewOrder">
        <text class="button-text">查看回收单</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRecycleOrderDetail } from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 批量回收信息
const batchInfo = ref({
  quantity: 1,
  delivery: 'pickup', // pickup: 快递上门, self: 自行寄出
  deliveryText: '快递上门',
  address: '',
  time: '',
  estimateTotal: '0.00'
})

// 订单信息
const orderInfo = ref({
  orderNo: '',
  status: '',
  createTime: ''
})

// 回收订单信息
const recycleOrderInfo = ref<any>({})

// 页面加载时获取参数
onLoad((options) => {
  console.log('批量成功页面接收到的参数:', options)
  pageParams.value = options || {}

  if (options.recycleOrderId) {
    loadOrderDetails(parseInt(options.recycleOrderId))
  } else {
    // 如果没有订单ID，显示错误信息
    uni.showToast({
      title: '订单信息缺失',
      icon: 'none'
    })
  }
})

// 加载订单详情
const loadOrderDetails = async (recycleOrderId: number) => {
  try {
    // 加载回收订单详情
    console.log('加载批量回收订单详情:', recycleOrderId)
    const recycleResponse = await getRecycleOrderDetail(recycleOrderId)

    if (recycleResponse.code === 1) {
      recycleOrderInfo.value = recycleResponse.data
      console.log('批量回收订单详情:', recycleResponse.data)

      // 设置订单基本信息
      orderInfo.value = {
        orderNo: recycleResponse.data.order_no || '',
        status: recycleResponse.data.status_text || '',
        createTime: recycleResponse.data.create_time_text || ''
      }

      // 设置批量回收信息
      batchInfo.value = {
        quantity: recycleResponse.data.quantity || 1,
        delivery: recycleResponse.data.delivery_type === 1 ? 'pickup' : 'self',
        deliveryText: recycleResponse.data.delivery_type === 1 ? '快递上门' : '自行寄出',
        address: recycleResponse.data.pickupAddress?.full_address || recycleResponse.data.pickup_address_detail || '',
        time: recycleResponse.data.pickup_time || '',
        estimateTotal: recycleResponse.data.expected_price?.toFixed(2) || '0.00'
      }
    } else {
      throw new Error(recycleResponse.msg || '获取回收订单详情失败')
    }

  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: error.message || '加载订单信息失败',
      icon: 'none'
    })
  }
}

// 获取完整地址
const getFullAddress = () => {
  // 优先使用pickupAddress对象中的full_address
  if (recycleOrderInfo.value.pickupAddress?.full_address) {
    return recycleOrderInfo.value.pickupAddress.full_address
  }

  // 其次使用pickup_address_detail（兼容旧数据）
  if (recycleOrderInfo.value.pickup_address_detail) {
    return recycleOrderInfo.value.pickup_address_detail
  }

  // 再次使用批量信息中的地址
  if (batchInfo.value.address) {
    return batchInfo.value.address
  }

  // 最后组合pickupAddress对象中的地址信息
  if (recycleOrderInfo.value.pickupAddress) {
    const pickup = recycleOrderInfo.value.pickupAddress
    const addressParts = []

    // 这里需要根据province_id等获取省市区名称，暂时使用address字段
    if (pickup.address) {
      return pickup.address
    }
  }

  // 如果有分段地址信息，组合显示（兼容旧数据）
  const addressParts = []
  if (recycleOrderInfo.value.pickup_province) {
    addressParts.push(recycleOrderInfo.value.pickup_province)
  }
  if (recycleOrderInfo.value.pickup_city) {
    addressParts.push(recycleOrderInfo.value.pickup_city)
  }
  if (recycleOrderInfo.value.pickup_district) {
    addressParts.push(recycleOrderInfo.value.pickup_district)
  }
  if (recycleOrderInfo.value.pickup_address) {
    addressParts.push(recycleOrderInfo.value.pickup_address)
  }

  return addressParts.length > 0 ? addressParts.join('') : ''
}

// 检查是否有地址分解信息
const hasAddressBreakdown = () => {
  // 检查pickupAddress对象中的ID信息（可以用来显示省市区）
  if (recycleOrderInfo.value.pickupAddress) {
    return recycleOrderInfo.value.pickupAddress.province_id ||
           recycleOrderInfo.value.pickupAddress.city_id ||
           recycleOrderInfo.value.pickupAddress.district_id
  }

  // 兼容旧数据格式
  return recycleOrderInfo.value.pickup_province ||
         recycleOrderInfo.value.pickup_city ||
         recycleOrderInfo.value.pickup_district
}

// 查看订单
const viewOrder = () => {
  if (recycleOrderInfo.value.id) {
    uni.navigateTo({
      url: `/addon/yz_she/pages/recycle_order/detail?id=${recycleOrderInfo.value.id}`
    })
  } else {
    uni.showToast({
      title: '订单信息缺失',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.batch-success-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  padding-top: 20rpx; // 增加顶部内边距，让整体布局更舒适
}

// 批量回收信息卡片
.product-card {
  background-color: #fff;
  margin: 24rpx 32rpx 20rpx 32rpx; // 设置上外边距，让视觉更舒适
  padding: 32rpx 24rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx; // 恢复四个圆角，视觉更柔和
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .batch-info {
    margin-bottom: 24rpx;

    .batch-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 1rpx solid rgba(255, 107, 53, 0.1);

      .batch-icon {
        width: 64rpx;
        height: 64rpx;
        background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx solid rgba(22, 160, 133, 0.2);
        box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.1);

        .icon-text {
          font-size: 24rpx;
        }
      }

      .batch-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4rpx;

        .batch-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
        }

        .batch-status {
          font-size: 22rpx;
          color: #52c41a;
          font-weight: 500;
        }
      }

      .quantity-display {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .quantity-number {
          font-size: 36rpx;
          color: #16a085;
          font-weight: 700;
        }

        .quantity-unit {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 500;
        }
      }
    }

    .delivery-info {
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .delivery-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 16rpx;

        .delivery-label {
          font-size: 24rpx;
          color: #666;
          min-width: 120rpx;
        }

        .delivery-value {
          font-size: 24rpx;
          color: #333;
          font-weight: 500;
          flex: 1;
          text-align: right;

          &.status-text {
            color: #16a085;
            font-weight: 600;
          }

          &.address-detail {
            font-weight: 600;
            color: #16a085;
            line-height: 1.4;
          }


        }
      }
    }

    // 地址分解信息
    .address-breakdown {
      margin-top: 16rpx;
      padding-top: 16rpx;
      border-top: 1rpx solid #f0f0f0;

      .delivery-item {
        margin-bottom: 8rpx;

        .delivery-label {
          font-size: 22rpx;
          color: #999;
        }

        .delivery-value {
          font-size: 22rpx;
          color: #666;
        }
      }
    }
  }

  .estimate-section {
    border-top: 1rpx solid rgba(255, 107, 53, 0.1);
    padding-top: 24rpx;

    .estimate-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .estimate-label {
        font-size: 24rpx;
        color: #666;
      }

      .estimate-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #ff6b35;
          font-weight: 600;
        }

        .estimate-value {
          font-size: 36rpx;
          color: #ff6b35;
          font-weight: 700;
        }
      }
    }

    .estimate-note {
      text-align: center;

      .note-text {
        font-size: 22rpx;
        color: #999;
        line-height: 1.4;
      }
    }
  }
}

// 成功状态区域
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 32rpx 60rpx 32rpx; // 调整内边距，顶部减少，底部保持
  text-align: center;

  .success-icon {
    position: relative;
    margin-bottom: 32rpx;

    .batch-bg {
      position: relative;
      width: 120rpx;
      height: 140rpx;

      .batch-body {
        width: 100%;
        height: 120rpx;
        background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
        border-radius: 12rpx;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);

        .batch-items {
          .item-stack {
            display: flex;
            flex-direction: column;
            gap: 4rpx;
            align-items: center;

            .stack-item {
              width: 40rpx;
              height: 8rpx;
              background-color: rgba(255, 255, 255, 0.8);
              border-radius: 4rpx;

              &:nth-child(1) {
                width: 48rpx;
              }

              &:nth-child(2) {
                width: 44rpx;
              }

              &:nth-child(3) {
                width: 36rpx;
              }
            }
          }
        }
      }
    }

    .check-mark {
      position: absolute;
      bottom: -8rpx;
      right: -8rpx;
      width: 40rpx;
      height: 40rpx;
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
      border: 3rpx solid #fff;
    }
  }

  .success-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .order-number {
    font-size: 26rpx;
    color: #16a085;
    font-weight: 600;
    margin-bottom: 16rpx;
  }

  .success-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    max-width: 500rpx;
    margin-bottom: 32rpx;
  }

  // 查看订单按钮
  .view-order-button {
    padding: 12rpx 32rpx;
    background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
    border: 2rpx solid #16a085;
    border-radius: 32rpx;
    color: #16a085;
    font-size: 24rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.1);

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
      box-shadow: 0 1rpx 4rpx rgba(22, 160, 133, 0.2);
    }

    .button-text {
      letter-spacing: 1rpx;
    }
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .product-card {
    margin: 16rpx 24rpx;
    padding: 24rpx 20rpx;
  }

  .success-section {
    padding: 40rpx 24rpx;
  }
}
</style>