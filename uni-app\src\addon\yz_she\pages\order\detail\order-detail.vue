<template>
  <view class="order-detail-page">
    <!-- 订单状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon" :class="getStatusIconClass(orderInfo.status)">
          <u-icon :name="getStatusIcon(orderInfo.status)" color="#fff" size="24"></u-icon>
        </view>
        <view class="status-info">
          <text class="status-title">{{ getStatusText(orderInfo.status) }}</text>
          <text class="status-desc">{{ getStatusDesc(orderInfo.status) }}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-bar">
        <view class="progress-line">
          <view class="progress-fill" :style="{ width: getProgressWidth(orderInfo.status) }"></view>
        </view>
        <view class="progress-steps">
          <view
            class="step-item"
            :class="{ active: step.status <= orderInfo.status, current: step.status === orderInfo.status }"
            v-for="step in progressSteps"
            :key="step.status"
          >
            <view class="step-dot"></view>
            <text class="step-text">{{ step.text }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-card">
      <view class="card-header">
        <text class="card-title">商品信息</text>
        <text class="order-no">{{ orderInfo.order_no }}</text>
      </view>

      <view class="product-info">
        <!-- 商品图片或图标 -->
        <image
          v-if="orderInfo.product_image"
          :src="orderInfo.product_image"
          class="product-image"
          mode="aspectFit"
        ></image>
        <view
          v-else
          class="product-image no-image"
        >
          <u-icon name="shopping-cart-fill" color="#16a085" size="40"></u-icon>
        </view>

        <view class="product-details">
          <text class="product-name">{{ orderInfo.product_name || '回收商品' }}</text>
          <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
          <view class="product-meta">
            <text class="meta-item">数量: {{ orderInfo.quantity }}件</text>
            <text class="meta-item">来源: {{ getSourceTypeText(orderInfo.source_type) }}</text>
          </view>
        </view>

        <view class="price-info">
          <text class="price-label">{{ getPriceLabel(orderInfo.status) }}</text>
          <view class="price-amount">
            <text class="currency">¥</text>
            <text class="price-value">{{ getCurrentPrice(orderInfo) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 配送信息卡片 -->
    <view class="delivery-card">
      <view class="card-header">
        <text class="card-title">配送信息</text>
      </view>

      <view class="delivery-info">
        <view class="delivery-item">
          <text class="delivery-label">配送方式</text>
          <text class="delivery-value">{{ getDeliveryTypeText(orderInfo.delivery_type) }}</text>
        </view>

        <!-- 快递上门信息 -->
        <template v-if="orderInfo.delivery_type === 1">
          <view class="delivery-item" v-if="orderInfo.pickup_contact_name">
            <text class="delivery-label">联系人</text>
            <text class="delivery-value">{{ orderInfo.pickup_contact_name }}</text>
          </view>
          <view class="delivery-item" v-if="orderInfo.pickup_contact_phone">
            <text class="delivery-label">联系电话</text>
            <text class="delivery-value">{{ orderInfo.pickup_contact_phone }}</text>
          </view>
          <view class="delivery-item" v-if="orderInfo.pickup_address_detail">
            <text class="delivery-label">取件地址</text>
            <text class="delivery-value address-detail">{{ orderInfo.pickup_address_detail }}</text>
          </view>
          <view class="delivery-item" v-if="orderInfo.pickup_time">
            <text class="delivery-label">预约时间</text>
            <text class="delivery-value">{{ orderInfo.pickup_time }}</text>
          </view>
        </template>

        <!-- 用户自寄信息 -->
        <template v-if="orderInfo.delivery_type === 2">
          <view class="delivery-item" v-if="orderInfo.express_company">
            <text class="delivery-label">快递公司</text>
            <text class="delivery-value">{{ orderInfo.express_company }}</text>
          </view>
          <view class="delivery-item" v-if="orderInfo.express_number">
            <text class="delivery-label">快递单号</text>
            <text class="delivery-value">{{ orderInfo.express_number }}</text>
          </view>
        </template>
      </view>
    </view>

    <!-- 价格明细卡片 -->
    <view class="price-card" v-if="showPriceDetail">
      <view class="card-header">
        <text class="card-title">价格明细</text>
      </view>

      <view class="price-detail">
        <view class="price-item">
          <text class="price-item-label">预期价格</text>
          <text class="price-item-value">¥{{ orderInfo.expected_price || '0.00' }}</text>
        </view>
        <view class="price-item" v-if="orderInfo.voucher_amount > 0">
          <text class="price-item-label">加价券</text>
          <text class="price-item-value voucher">+¥{{ orderInfo.voucher_amount }}</text>
        </view>
        <view class="price-item" v-if="orderInfo.final_price !== null">
          <text class="price-item-label">质检价格</text>
          <text class="price-item-value">¥{{ orderInfo.final_price }}</text>
        </view>
        <view class="price-item total" v-if="orderInfo.total_amount !== null">
          <text class="price-item-label">最终金额</text>
          <text class="price-item-value">¥{{ orderInfo.total_amount }}</text>
        </view>
      </view>
    </view>

    <!-- 质检信息卡片 -->
    <view class="quality-card" v-if="showQualityInfo">
      <view class="card-header">
        <text class="card-title">质检信息</text>
      </view>

      <view class="quality-info">
        <view class="quality-item" v-if="orderInfo.quality_score !== null">
          <text class="quality-label">质检评分</text>
          <view class="quality-score">
            <text class="score-value">{{ orderInfo.quality_score }}</text>
            <text class="score-unit">分</text>
          </view>
        </view>
        <view class="quality-item" v-if="orderInfo.quality_note">
          <text class="quality-label">质检说明</text>
          <text class="quality-note">{{ orderInfo.quality_note }}</text>
        </view>
      </view>
    </view>

    <!-- 订单操作按钮 -->
    <view class="action-buttons" v-if="hasActions">
      <view
        class="action-btn"
        :class="action.type"
        v-for="action in getOrderActions(orderInfo.status)"
        :key="action.type"
        @click="handleAction(action.type)"
      >
        <text class="action-text">{{ action.text }}</text>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getRecycleOrderDetail, cancelRecycleOrder, confirmPriceAndSettle, requestReturn } from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

// 页面参数
const orderId = ref('')
const loading = ref(false)

// 订单信息
const orderInfo = ref<any>({
  id: 0,
  order_no: '',
  status: 1,
  product_name: '',
  product_code: '',
  product_image: '',
  quantity: 1,
  expected_price: 0,
  final_price: null,
  total_amount: null,
  voucher_amount: 0,
  source_type: 1,
  delivery_type: 1,
  pickup_contact_name: '',
  pickup_contact_phone: '',
  pickup_address_detail: '',
  pickup_time: '',
  express_company: '',
  express_number: '',
  quality_score: null,
  quality_note: '',
  create_time: '',
  update_time: ''
})

// 进度步骤
const progressSteps = ref([
  { status: 1, text: '待取件' },
  { status: 2, text: '待收货' },
  { status: 3, text: '待质检' },
  { status: 4, text: '待确认' },
  { status: 7, text: '已完成' }
])

// 页面加载
onLoad((options) => {
  if (options.id) {
    orderId.value = options.id
    loadOrderDetail()
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const response = await getRecycleOrderDetail(parseInt(orderId.value))
    if (response.code === 1) {
      orderInfo.value = {
        ...response.data,
        product_image: response.data.product_image ? img(response.data.product_image) : ''
      }
    } else {
      uni.showToast({
        title: response.msg || '获取订单详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取状态文字
const getStatusText = (status: number) => {
  const statusMap = {
    1: '待取件',
    2: '待收货',
    3: '待质检',
    4: '待确认',
    5: '待退回',
    6: '已退回',
    7: '已完成',
    8: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态描述
const getStatusDesc = (status: number) => {
  const descMap = {
    1: '等待快递员上门取件',
    2: '商品已寄出，等待平台收货',
    3: '平台已收货，正在进行质检',
    4: '质检完成，等待您确认价格',
    5: '商品正在退回中',
    6: '商品已退回',
    7: '订单已完成，感谢您的使用',
    8: '订单已取消'
  }
  return descMap[status] || ''
}

// 获取状态图标
const getStatusIcon = (status: number) => {
  const iconMap = {
    1: 'clock',
    2: 'car',
    3: 'search',
    4: 'checkmark-circle',
    5: 'return-left',
    6: 'return-left',
    7: 'checkmark-circle',
    8: 'close-circle'
  }
  return iconMap[status] || 'clock'
}

// 获取状态图标样式类
const getStatusIconClass = (status: number) => {
  if (status === 8) return 'status-cancelled'
  if (status === 7) return 'status-completed'
  if (status >= 5) return 'status-warning'
  return 'status-processing'
}

// 获取进度宽度
const getProgressWidth = (status: number) => {
  const progressMap = {
    1: '20%',
    2: '40%',
    3: '60%',
    4: '80%',
    5: '60%', // 退回状态回到质检阶段
    6: '60%',
    7: '100%',
    8: '0%'
  }
  return progressMap[status] || '0%'
}

// 获取来源类型文字
const getSourceTypeText = (sourceType: number) => {
  const sourceMap = {
    1: '估价订单',
    2: '直接回收',
    3: '批量下单'
  }
  return sourceMap[sourceType] || '未知来源'
}

// 获取配送方式文字
const getDeliveryTypeText = (deliveryType: number) => {
  return deliveryType === 1 ? '快递上门' : '用户自寄'
}

// 获取价格标签
const getPriceLabel = (status: number) => {
  if (status >= 7) return '最终价格'
  if (status >= 4) return '质检价格'
  return '预期价格'
}

// 获取当前价格
const getCurrentPrice = (order: any) => {
  if (order.total_amount !== null) return order.total_amount.toFixed(2)
  if (order.final_price !== null) return order.final_price.toFixed(2)
  return order.expected_price?.toFixed(2) || '0.00'
}

// 是否显示价格明细
const showPriceDetail = computed(() => {
  return orderInfo.value.voucher_amount > 0 ||
         orderInfo.value.final_price !== null ||
         orderInfo.value.total_amount !== null
})

// 是否显示质检信息
const showQualityInfo = computed(() => {
  return orderInfo.value.quality_score !== null || orderInfo.value.quality_note
})

// 是否有操作按钮
const hasActions = computed(() => {
  return getOrderActions(orderInfo.value.status).length > 0
})

// 获取订单操作按钮
const getOrderActions = (status: number) => {
  const actions = []

  switch (status) {
    case 1: // 待取件
      actions.push({ type: 'cancel', text: '取消订单' })
      break
    case 4: // 待确认
      actions.push({ type: 'reject', text: '申请退回' })
      actions.push({ type: 'confirm', text: '确认价格' })
      break
    case 2: // 待收货
    case 3: // 待质检
      actions.push({ type: 'contact', text: '联系客服' })
      break
  }

  return actions
}

// 处理操作按钮点击
const handleAction = async (actionType: string) => {
  switch (actionType) {
    case 'cancel':
      await handleCancelOrder()
      break
    case 'confirm':
      await handleConfirmPrice()
      break
    case 'reject':
      await handleRejectPrice()
      break
    case 'contact':
      handleContactService()
      break
  }
}

// 取消订单
const handleCancelOrder = async () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await cancelRecycleOrder(orderInfo.value.id)
          if (response.code === 1) {
            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            })
            loadOrderDetail() // 重新加载订单详情
          } else {
            uni.showToast({
              title: response.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          uni.showToast({
            title: '取消失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 确认价格
const handleConfirmPrice = async () => {
  uni.showModal({
    title: '确认价格',
    content: `确认接受质检价格 ¥${orderInfo.value.final_price?.toFixed(2)} 吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await confirmPriceAndSettle(orderInfo.value.id)
          if (response.code === 1) {
            uni.showToast({
              title: '确认成功',
              icon: 'success'
            })
            loadOrderDetail() // 重新加载订单详情
          } else {
            uni.showToast({
              title: response.msg || '确认失败',
              icon: 'none'
            })
          }
        } catch (error) {
          uni.showToast({
            title: '确认失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 申请退回
const handleRejectPrice = async () => {
  uni.showModal({
    title: '申请退回',
    content: '不接受质检价格，申请退回商品？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await requestReturn(orderInfo.value.id, '不接受质检价格')
          if (response.code === 1) {
            uni.showToast({
              title: '申请成功',
              icon: 'success'
            })
            loadOrderDetail() // 重新加载订单详情
          } else {
            uni.showToast({
              title: response.msg || '申请失败',
              icon: 'none'
            })
          }
        } catch (error) {
          uni.showToast({
            title: '申请失败，请重试',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 联系客服
const handleContactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '如有疑问，请联系客服：400-123-4567',
    showCancel: false
  })
}
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding-bottom: 120rpx; // 为底部按钮留出空间
}

// 订单状态卡片
.status-card {
  background-color: #fff;
  margin: 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .status-header {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 32rpx;

    .status-icon {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.status-processing {
        background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      }

      &.status-completed {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      }

      &.status-warning {
        background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
      }

      &.status-cancelled {
        background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 700;
        display: block;
        margin-bottom: 8rpx;
      }

      .status-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }

  .progress-bar {
    .progress-line {
      height: 4rpx;
      background-color: #f0f0f0;
      border-radius: 2rpx;
      position: relative;
      margin-bottom: 20rpx;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #16a085 0%, #1abc9c 100%);
        border-radius: 2rpx;
        transition: width 0.3s ease;
      }
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;

        .step-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: #f0f0f0;
          transition: all 0.3s ease;
        }

        .step-text {
          font-size: 20rpx;
          color: #999;
          transition: color 0.3s ease;
        }

        &.active {
          .step-dot {
            background-color: #16a085;
          }

          .step-text {
            color: #16a085;
          }
        }

        &.current {
          .step-dot {
            background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
            box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.4);
          }

          .step-text {
            color: #16a085;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 卡片通用样式
.product-card,
.delivery-card,
.price-card,
.quality-card {
  background-color: #fff;
  margin: 0 32rpx 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

    .card-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
    }

    .order-no {
      font-size: 24rpx;
      color: #16a085;
      font-weight: 500;
      font-family: 'Courier New', monospace;
    }
  }
}

// 商品信息
.product-card {
  .product-info {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      background-color: #f0f0f0;

      &.no-image {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
        border: 2rpx solid rgba(22, 160, 133, 0.2);
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-code {
        font-size: 24rpx;
        color: #999;
      }

      .product-meta {
        display: flex;
        gap: 16rpx;

        .meta-item {
          font-size: 22rpx;
          color: #666;
          padding: 4rpx 8rpx;
          background-color: #f8f9fa;
          border-radius: 4rpx;
        }
      }
    }

    .price-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4rpx;

      .price-label {
        font-size: 22rpx;
        color: #666;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 2rpx;

        .currency {
          font-size: 22rpx;
          color: #16a085;
          font-weight: 600;
        }

        .price-value {
          font-size: 32rpx;
          color: #16a085;
          font-weight: 700;
        }
      }
    }
  }
}

// 配送信息
.delivery-card {
  .delivery-info {
    .delivery-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .delivery-label {
        font-size: 26rpx;
        color: #666;
        min-width: 120rpx;
      }

      .delivery-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
        flex: 1;
        text-align: right;

        &.address-detail {
          font-weight: 600;
          color: #16a085;
          line-height: 1.4;
        }
      }
    }
  }
}

// 价格明细
.price-card {
  .price-detail {
    .price-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      &.total {
        padding-top: 16rpx;
        border-top: 1rpx solid #f0f0f0;
        font-weight: 600;

        .price-item-value {
          font-size: 32rpx;
          color: #16a085;
        }
      }

      .price-item-label {
        font-size: 26rpx;
        color: #666;
      }

      .price-item-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;

        &.voucher {
          color: #fa8c16;
        }
      }
    }
  }
}

// 质检信息
.quality-card {
  .quality-info {
    .quality-item {
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .quality-label {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 12rpx;
        display: block;
      }

      .quality-score {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .score-value {
          font-size: 48rpx;
          color: #16a085;
          font-weight: 700;
        }

        .score-unit {
          font-size: 24rpx;
          color: #666;
        }
      }

      .quality-note {
        font-size: 26rpx;
        color: #333;
        line-height: 1.5;
        padding: 16rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
        border: 1rpx solid #e9ecef;
      }
    }
  }
}

// 操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 32rpx;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 20rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    // 取消/申请退回按钮
    &.cancel,
    &.reject {
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      border: 2rpx solid #d9d9d9;
      color: #666;

      &:active {
        background: linear-gradient(135deg, #f8f9fa 0%, #f0f0f0 100%);
      }
    }

    // 确认按钮
    &.confirm {
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      color: #fff;
      box-shadow: 0 4rpx 16rpx rgba(22, 160, 133, 0.3);

      &:active {
        background: linear-gradient(135deg, #0d7377 0%, #16a085 100%);
      }
    }

    // 联系客服按钮
    &.contact {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: #fff;
      box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);

      &:active {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
      }
    }

    .action-text {
      letter-spacing: 1rpx;
    }
  }
}

// 小屏幕适配
@media (max-width: 750rpx) {
  .status-card,
  .product-card,
  .delivery-card,
  .price-card,
  .quality-card {
    margin: 0 24rpx 16rpx 24rpx;
    padding: 24rpx 20rpx;
  }

  .product-info {
    .product-image {
      width: 100rpx;
      height: 100rpx;
    }

    .product-details {
      .product-name {
        font-size: 26rpx;
      }
    }

    .price-info {
      .price-amount {
        .price-value {
          font-size: 28rpx;
        }
      }
    }
  }

  .action-buttons {
    padding: 16rpx 24rpx;

    .action-btn {
      height: 80rpx;
      font-size: 26rpx;
    }
  }
}
</style>