<template>
  <view class="quote-detail-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 订单内容 -->
    <view v-else-if="orderInfo.id" class="order-content">

      <!-- 已完成状态 - 成功页面布局 -->
      <view v-if="orderInfo.status === 5" class="success-layout">
        <!-- 顶部提示栏和商品卡片组合 -->
        <view class="top-card-container">
          <!-- 顶部提示栏 -->
          <view class="top-notice success">
            <text class="notice-text">交易完成 感谢您的信任</text>
            <text class="notice-right">已完成</text>
          </view>

          <!-- 商品信息卡片 -->
          <view class="product-card">
            <view class="product-info">
              <view class="product-image">
                <image
                  v-if="orderInfo.product_image"
                  :src="img(orderInfo.product_image)"
                  class="product-img"
                  mode="aspectFill"
                />
                <view v-else class="image-placeholder">
                  <text class="placeholder-text">商品图片</text>
                </view>
              </view>
              <view class="product-details">
                <text class="product-name">{{ orderInfo.product_name || '商品名称' }}</text>
                <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
              </view>
              <view class="brand-logo">
                <u-icon name="checkmark-circle-fill" color="#52c41a" size="24"></u-icon>
              </view>
            </view>

            <!-- 价格信息 -->
            <view class="price-section">
              <view class="price-info">
                <text class="price-label">实际回收</text>
                <view class="price-amount">
                  <text class="currency">¥</text>
                  <text class="price-value">{{ orderInfo.quote_price || '0.00' }}</text>
                </view>
              </view>

              <view class="price-breakdown" v-if="orderInfo.quote_price">
                <text class="breakdown-item">评估金额: ¥{{ orderInfo.quote_price }}</text>
                <text class="breakdown-item">订单号: {{ orderInfo.order_no }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 成功状态区域 -->
        <view class="success-section">
          <view class="success-icon">
            <view class="clipboard-bg">
              <view class="clipboard-body">
                <view class="clipboard-lines">
                  <view class="line"></view>
                  <view class="line"></view>
                  <view class="line"></view>
                </view>
              </view>
              <view class="clipboard-clip"></view>
            </view>
            <view class="check-mark">
              <u-icon name="checkmark" color="#fff" size="16"></u-icon>
            </view>
          </view>

          <text class="success-title">订单创建成功</text>
          <text class="order-number">订单号: {{ orderInfo.order_no }}</text>
          <text class="success-desc">您可以在我的订单中查看该订单的详细信息</text>

          <!-- 查看订单按钮 -->
          <view class="view-order-button" @click="viewOrderList">
            <text class="button-text">查看订单</text>
          </view>
        </view>
      </view>

      <!-- 估价中状态 - 重新设计的简洁UI -->
      <view v-if="orderInfo.status === 1" class="evaluating-layout">
        <!-- 状态指示器 -->
        <view class="status-header">
          <view class="status-icon">
            <view class="pulse-ring"></view>
            <view class="pulse-ring delay-1"></view>
            <view class="pulse-ring delay-2"></view>
            <text class="status-emoji">🔍</text>
          </view>
          <view class="status-info">
            <text class="status-title">专业评估中</text>
            <text class="status-desc">评估师正在仔细分析您的商品</text>
            <text class="status-time">预计 {{ getEstimatedTime() }} 完成</text>
          </view>
        </view>

        <!-- 综合信息卡片 -->
        <view class="comprehensive-card">
          <!-- 商品基本信息 -->
          <view class="product-section">
            <view class="section-header">
              <text class="section-title">商品信息</text>
              <view class="order-number">
                <text class="order-label">订单号</text>
                <text class="order-value">{{ orderInfo.order_no }}</text>
              </view>
            </view>

            <view class="product-main">
              <view class="product-image-wrapper">
                <image
                  v-if="orderInfo.product_image"
                  :src="img(orderInfo.product_image)"
                  class="product-image"
                  mode="aspectFill"
                />
                <view v-else class="image-placeholder">
                  <text class="placeholder-icon">📱</text>
                  <text class="placeholder-text">商品图片</text>
                </view>
                <view class="evaluation-badge">
                  <text class="badge-text">评估中</text>
                </view>
              </view>

              <view class="product-info">
                <text class="product-name">{{ orderInfo.product_name || '商品名称' }}</text>
                <text class="product-model" v-if="orderInfo.product_code">型号：{{ orderInfo.product_code }}</text>

                <view class="price-section">
                  <text class="price-label">预估价格</text>
                  <view class="price-display">
                    <text class="currency">¥</text>
                    <text class="price-amount">{{ orderInfo.quote_price || '评估中' }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 评估照片 -->
          <view class="photos-section" v-if="evaluateInfo.photos.length > 0">
            <view class="section-header">
              <text class="section-title">评估照片</text>
              <text class="photo-count">{{ evaluateInfo.photoCount }}张</text>
            </view>
            <view class="photos-grid">
              <view
                class="photo-item"
                v-for="(photo, index) in evaluateInfo.photos.slice(0, 6)"
                :key="index"
                @click="previewPhoto(index)"
              >
                <image :src="photo" class="photo-image" mode="aspectFill" />
              </view>
              <view class="more-photos" v-if="evaluateInfo.photoCount > 6" @click="previewPhoto(0)">
                <text class="more-text">+{{ evaluateInfo.photoCount - 6 }}</text>
              </view>
            </view>
          </view>

          <!-- 配件信息 -->
          <view class="accessories-section" v-if="orderInfo.accessories && orderInfo.accessories.length > 0">
            <view class="section-header">
              <text class="section-title">配件信息</text>
            </view>
            <view class="accessories-grid">
              <view
                class="accessory-item"
                v-for="(accessory, index) in orderInfo.accessories"
                :key="index"
              >
                <view class="accessory-icon">✓</view>
                <text class="accessory-name">{{ accessory.name }}</text>
              </view>
            </view>
          </view>

          <!-- 备注信息 -->
          <view class="notes-section" v-if="orderInfo.user_note || orderInfo.admin_note">
            <view class="section-header">
              <text class="section-title">备注信息</text>
            </view>
            <view class="notes-content">
              <view class="note-item" v-if="orderInfo.user_note">
                <text class="note-label">用户备注</text>
                <text class="note-text">{{ orderInfo.user_note }}</text>
              </view>
              <view class="note-item" v-if="orderInfo.admin_note">
                <text class="note-label">客服备注</text>
                <text class="note-text">{{ orderInfo.admin_note }}</text>
              </view>
            </view>
          </view>

          <!-- 温馨提示 -->
          <view class="tips-section">
            <view class="tips-icon">💡</view>
            <view class="tips-content">
              <text class="tips-title">温馨提示</text>
              <text class="tips-text">我们的专业评估师会根据商品的外观、功能、配件等多个维度进行综合评估，最终价格以评估结果为准。</text>
            </view>
          </view>
        </view>

        <!-- 评估进度 -->
        <view class="progress-card">
          <view class="progress-header">
            <text class="progress-title">评估进度</text>
            <text class="progress-percentage">{{ Math.round(evaluationProgress) }}%</text>
          </view>

          <view class="progress-bar-container">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: evaluationProgress + '%' }"></view>
            </view>
          </view>

          <view class="progress-steps">
            <view class="step-item" :class="{ active: evaluationProgress >= 25, completed: evaluationProgress > 25 }">
              <view class="step-dot"></view>
              <text class="step-label">接收订单</text>
            </view>
            <view class="step-item" :class="{ active: evaluationProgress >= 50, completed: evaluationProgress > 50 }">
              <view class="step-dot"></view>
              <text class="step-label">照片分析</text>
            </view>
            <view class="step-item" :class="{ active: evaluationProgress >= 75, completed: evaluationProgress > 75 }">
              <view class="step-dot"></view>
              <text class="step-label">专业评估</text>
            </view>
            <view class="step-item" :class="{ active: evaluationProgress >= 100 }">
              <view class="step-dot"></view>
              <text class="step-label">完成报价</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他状态 - 详情页面布局 -->
      <view v-else-if="orderInfo.status !== 1" class="detail-layout">
        <!-- 顶部提示栏和商品卡片组合 -->
        <view class="top-card-container">
          <!-- 顶部提示栏 -->
          <view class="top-notice" :class="getStatusClass(orderInfo.status)">
            <text class="notice-text">{{ getStatusNotice(orderInfo.status) }}</text>
            <text class="notice-right">{{ getStatusTitle(orderInfo.status) }}</text>
          </view>

          <!-- 商品信息卡片 -->
          <view class="product-card">
            <view class="product-info">
              <view class="product-image">
                <image
                  v-if="orderInfo.product_image"
                  :src="img(orderInfo.product_image)"
                  class="product-img"
                  mode="aspectFill"
                />
                <view v-else class="image-placeholder">
                  <text class="placeholder-text">商品图片</text>
                </view>
              </view>
              <view class="product-details">
                <text class="product-name">{{ orderInfo.product_name || '商品名称' }}</text>
                <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
                <text class="order-no">订单号：{{ orderInfo.order_no }}</text>
              </view>
            </view>

            <!-- 价格信息 -->
            <view class="price-section">
              <view class="price-item">
                <text class="price-label">{{ getPriceLabel(orderInfo.status) }}</text>
                <view class="price-amount">
                  <text class="currency">¥</text>
                  <text class="price-value">{{ orderInfo.quote_price || '待估价' }}</text>
                </view>
              </view>
              <view class="price-note" v-if="orderInfo.status === 1">
                <text class="note-text">最终价格以专业评估师鉴定为准</text>
              </view>
              <view class="price-note" v-if="orderInfo.status === 2">
                <text class="note-text">请确认价格，确认后请及时寄出商品</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 评估照片区域 -->
        <view class="photos-card" v-if="evaluateInfo.photos.length > 0">
          <view class="card-header">
            <view class="header-left">
              <u-icon name="camera" color="#0d7377" size="20"></u-icon>
              <text class="card-title">评估照片</text>
            </view>
            <text class="photo-count">{{ evaluateInfo.photoCount }}张</text>
          </view>
          <view class="photo-grid">
            <view
              class="photo-item"
              v-for="(photo, index) in evaluateInfo.photos"
              :key="index"
              @click="previewPhoto(index)"
            >
              <image
                :src="photo"
                class="photo-image"
                mode="aspectFill"
              />
            </view>
            <view class="more-photos" v-if="evaluateInfo.photoCount > 6">
              <text class="more-text">+{{ evaluateInfo.photoCount - 6 }}</text>
            </view>
          </view>
        </view>

        <!-- 配件信息 -->
        <view class="accessories-card" v-if="orderInfo.accessories && orderInfo.accessories.length > 0">
          <view class="card-header">
            <view class="header-left">
              <u-icon name="grid" color="#0d7377" size="20"></u-icon>
              <text class="card-title">配件信息</text>
            </view>
          </view>
          <view class="accessories-list">
            <view
              class="accessory-item"
              v-for="(accessory, index) in orderInfo.accessories"
              :key="index"
            >
              <view class="accessory-icon">
                <u-icon name="checkmark-circle" color="#52c41a" size="16"></u-icon>
              </view>
              <text class="accessory-name">{{ accessory.name }}</text>
            </view>
          </view>
        </view>

        <!-- 备注信息 -->
        <view class="notes-card" v-if="orderInfo.user_note || orderInfo.admin_note">
          <view class="card-header">
            <view class="header-left">
              <u-icon name="chat" color="#0d7377" size="20"></u-icon>
              <text class="card-title">备注信息</text>
            </view>
          </view>
          <view class="notes-content">
            <view class="note-item" v-if="orderInfo.user_note">
              <text class="note-label">用户备注：</text>
              <text class="note-text">{{ orderInfo.user_note }}</text>
            </view>
            <view class="note-item" v-if="orderInfo.admin_note">
              <text class="note-label">管理员备注：</text>
              <text class="note-text">{{ orderInfo.admin_note }}</text>
            </view>
          </view>
        </view>

        <!-- 报价信息 (待确认状态) -->
        <view class="quote-card" v-if="orderInfo.status === 2">
          <view class="card-header">
            <view class="header-left">
              <u-icon name="calculator" color="#0d7377" size="20"></u-icon>
              <text class="card-title">报价详情</text>
            </view>
          </view>
          <view class="quote-content">
            <view class="quote-item">
              <text class="quote-label">评估价格</text>
              <text class="quote-value">¥{{ orderInfo.quote_price }}</text>
            </view>
            <view class="quote-item" v-if="orderInfo.quote_time">
              <text class="quote-label">评估时间</text>
              <text class="quote-value">{{ formatTime(orderInfo.quote_time) }}</text>
            </view>
            <view class="quote-note">
              <u-icon name="info-circle" color="#ff6b35" size="16"></u-icon>
              <text class="note-text">请在24小时内确认价格，超时将自动取消订单</text>
            </view>
          </view>
        </view>
      </view>

    <!-- 物流信息卡片 (待发货状态) -->
    <view class="logistics-card" v-if="orderInfo.status === 3">
      <view class="card-header">
        <text class="card-title">物流信息</text>
      </view>

      <!-- 收货地址 -->
      <view class="address-section">
        <view class="section-title">
          <text class="title-text">收货地址</text>
        </view>
        <view class="address-info">
          <text class="address-name">放心星仓库 13060000687</text>
          <text class="address-detail">四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递</text>
          <view class="copy-btn" @click="copyAddress">
            <text class="copy-text">复制</text>
          </view>
        </view>
      </view>

      <!-- 快递信息填写 -->
      <view class="express-section">
        <view class="express-item" @click="showExpressModal">
          <view class="item-label">
            <text class="label-text">快递公司</text>
          </view>
          <view class="item-content">
            <text class="content-text" :class="{ placeholder: !selectedExpress }">
              {{ selectedExpress || '请选择快递公司' }}
            </text>
            <text class="arrow">></text>
          </view>
        </view>

        <view class="express-item">
          <view class="item-label">
            <text class="label-text">快递单号</text>
          </view>
          <view class="item-content">
            <input
              class="tracking-input"
              v-model="trackingNumber"
              placeholder="请输入快递单号"
              maxlength="30"
            />
          </view>
        </view>
      </view>

      <!-- 发货按钮 -->
      <view class="ship-button" :class="{ disabled: !canShip }" @click="submitShipping">
        <text class="button-text">确认发货</text>
      </view>
    </view>

    <!-- 订单进度 (已发货及以后状态) -->
    <view class="progress-card" v-if="orderInfo.status >= 4">
      <view class="card-header">
        <text class="card-title">订单进度</text>
      </view>
      <view class="progress-timeline">
        <view
          class="timeline-item"
          v-for="(step, index) in orderSteps"
          :key="index"
          :class="{ active: step.completed, current: step.current }"
        >
          <view class="step-dot"></view>
          <view class="step-content">
            <text class="step-title">{{ step.title }}</text>
            <text class="step-time" v-if="step.time">{{ step.time }}</text>
          </view>
        </view>
      </view>
    </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-buttons" v-if="hasActions">
      <view
        class="action-btn"
        v-for="action in getOrderActions(orderInfo.status)"
        :key="action.type"
        :class="action.type"
        @click="handleAction(action.type)"
      >
        <text class="btn-text">{{ action.text }}</text>
      </view>
    </view>

    <!-- 快递公司选择弹窗 -->
    <view class="express-modal" v-if="showExpressSelect" @click="hideExpressModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">选择快递公司</text>
          <view class="close-btn" @click="hideExpressModal">×</view>
        </view>
        <view class="express-list">
          <view
            class="express-option"
            v-for="express in expressList"
            :key="express"
            @click="selectExpress(express)"
          >
            <text class="express-name">{{ express }}</text>
            <view class="express-check" v-if="selectedExpress === express">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <view class="error-content">
        <view class="error-icon">😕</view>
        <text class="error-text">订单信息加载失败</text>
        <view class="retry-button" @click="loadOrderDetail">
          <text class="retry-text">重新加载</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderDetail, confirmQuoteOrder, cancelQuoteOrder } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 页面参数
const orderId = ref('')
const loading = ref(false)

// 订单信息
const orderInfo = ref({
  id: '',
  order_no: '',
  status: 1, // 1:评估中 2:待确认 3:待发货 4:已发货 5:已完成 6:已取消
  product_name: '',
  product_code: '',
  product_image: '',
  quote_price: '',
  create_time: '',
  quote_time: '',
  confirm_time: '',
  ship_time: '',
  complete_time: '',
  cancel_time: '',
  user_note: '',
  admin_note: '',
  photos: [],
  accessories: []
})

// 评估信息
const evaluateInfo = ref({
  photoCount: 0,
  photos: []
})

// 物流信息
const selectedExpress = ref('')
const trackingNumber = ref('')
const showExpressSelect = ref(false)

// 快递公司列表
const expressList = [
  '顺丰速运',
  '京东物流',
  '德邦快递',
  '中通快递',
  '韵达速递',
  '圆通速递',
  '申通快递',
  '极兔速递'
]

// 订单步骤
const orderSteps = computed(() => {
  const steps = [
    { title: '订单创建', completed: true, time: formatTime(orderInfo.value.create_time) },
    { title: '评估完成', completed: orderInfo.value.status >= 2, time: formatTime(orderInfo.value.quote_time) },
    { title: '用户确认', completed: orderInfo.value.status >= 3, time: formatTime(orderInfo.value.confirm_time) },
    { title: '商品发货', completed: orderInfo.value.status >= 4, time: formatTime(orderInfo.value.ship_time) },
    { title: '交易完成', completed: orderInfo.value.status >= 5, time: formatTime(orderInfo.value.complete_time) }
  ]

  // 标记当前步骤
  const currentIndex = steps.findIndex(step => !step.completed)
  if (currentIndex >= 0 && currentIndex > 0) {
    steps[currentIndex - 1].current = true
  } else if (currentIndex === -1 && orderInfo.value.status < 6) {
    // 所有步骤都完成但状态不是已取消，标记最后一个为当前
    steps[steps.length - 1].current = true
  }

  return steps
})

// 是否可以发货
const canShip = computed(() => {
  return selectedExpress.value && trackingNumber.value.trim()
})

// 是否有操作按钮
const hasActions = computed(() => {
  if (!orderInfo.value.id) return false
  const actions = getOrderActions(orderInfo.value.status)
  return actions.length > 0
})

// 获取状态样式类
const getStatusClass = (status: number) => {
  const statusMap = {
    1: 'evaluating',    // 评估中
    2: 'completed',     // 待确认
    3: 'pending-ship',  // 待发货
    4: 'shipped',       // 已发货
    5: 'finished',      // 已完成
    6: 'cancelled'      // 已取消
  }
  return statusMap[status] || 'evaluating'
}

// 获取状态图标
const getStatusIcon = (status: number) => {
  const iconMap = {
    1: '⏳',
    2: '✅',
    3: '📦',
    4: '🚚',
    5: '🎉',
    6: '❌'
  }
  return iconMap[status] || '⏳'
}

// 获取状态标题
const getStatusTitle = (status: number) => {
  const titleMap = {
    1: '评估中',
    2: '待确认',
    3: '待发货',
    4: '已发货',
    5: '已完成',
    6: '已取消'
  }
  return titleMap[status] || '评估中'
}

// 获取状态描述
const getStatusDesc = (status: number) => {
  const descMap = {
    1: '专业评估师正在评估中，请耐心等待',
    2: '评估已完成，请确认价格',
    3: '请填写物流信息并发货',
    4: '商品已发货，等待平台收货',
    5: '交易已完成，感谢您的信任',
    6: '订单已取消'
  }
  return descMap[status] || '评估中'
}

// 获取状态提示文字
const getStatusNotice = (status: number) => {
  const noticeMap = {
    1: '评估中 专业评估师正在评估',
    2: '评估完成 请确认价格',
    3: '待发货 请填写物流信息',
    4: '已发货 商品运输中',
    5: '已完成 交易成功',
    6: '已取消 订单已取消'
  }
  return noticeMap[status] || '评估中 专业评估师正在评估'
}

// 获取价格标签
const getPriceLabel = (status: number) => {
  if (status === 1) return '预估价格'
  if (status === 6) return '取消价格'
  return '回收价格'
}

// 获取订单操作
const getOrderActions = (status: number) => {
  const actionsMap = {
    1: [], // 评估中 - 不显示底部按钮
    2: [
      { type: 'confirm', text: '确认回收' },
      { type: 'cancel', text: '取消订单' }
    ], // 待确认
    3: [], // 待发货（在卡片内操作）
    4: [{ type: 'logistics', text: '查看物流' }], // 已发货
    5: [{ type: 'reorder', text: '再次回收' }], // 已完成
    6: [{ type: 'reorder', text: '再次回收' }] // 已取消
  }
  return actionsMap[status] || []
}

// 页面加载
onLoad((options) => {
  if (options.id) {
    orderId.value = options.id
    loadOrderDetail()
  }
  if (options.status) {
    orderInfo.value.status = parseInt(options.status)
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const response = await getQuoteOrderDetail(parseInt(orderId.value))
    if (response.code === 1) {
      const data = response.data
      orderInfo.value = {
        ...data,
        photos: data.photos || [],
        accessories: data.accessories || []
      }

      // 处理评估照片
      if (data.photos && data.photos.length > 0) {
        evaluateInfo.value.photos = data.photos.map(photo => img(photo.photo_url))
        evaluateInfo.value.photoCount = data.photos.length
      }

      // 如果是评估中状态，启动进度动画
      if (data.status === 1) {
        startProgressAnimation()
      }
    } else {
      uni.showToast({
        title: response.msg || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 预览照片
const previewPhoto = (index: number) => {
  uni.previewImage({
    urls: evaluateInfo.value.photos,
    current: index
  })
}

// 复制地址
const copyAddress = () => {
  const address = '放心星仓库 13060000687 四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递'
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    }
  })
}

// 显示快递选择弹窗
const showExpressModal = () => {
  showExpressSelect.value = true
}

// 隐藏快递选择弹窗
const hideExpressModal = () => {
  showExpressSelect.value = false
}

// 选择快递公司
const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
  uni.showToast({
    title: `已选择${express}`,
    icon: 'success'
  })
}

// 提交发货信息
const submitShipping = () => {
  if (!canShip.value) {
    uni.showToast({
      title: '请完善物流信息',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '确认发货',
    content: `快递公司：${selectedExpress.value}\n快递单号：${trackingNumber.value}`,
    success: (res) => {
      if (res.confirm) {
        // 提交发货信息
        console.log('提交发货信息')
        uni.showToast({
          title: '发货成功',
          icon: 'success'
        })
        // 更新订单状态
        orderInfo.value.status = 4
        orderInfo.value.ship_time = new Date().toLocaleString()
      }
    }
  })
}

// 确认订单
const confirmOrder = async () => {
  uni.showModal({
    title: '确认回收',
    content: `确认以¥${orderInfo.value.quote_price}的价格回收此商品？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await confirmQuoteOrder(parseInt(orderId.value))
          if (response.code === 1) {
            uni.showToast({
              title: '确认成功，请寄出商品',
              icon: 'success'
            })
            // 重新加载订单信息
            loadOrderDetail()
          } else {
            uni.showToast({
              title: response.msg || '确认失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('确认订单失败:', error)
          uni.showToast({
            title: '确认失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 取消订单
const cancelOrder = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await cancelQuoteOrder(parseInt(orderId.value), '用户主动取消')
          if (response.code === 1) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            // 重新加载订单信息
            loadOrderDetail()
          } else {
            uni.showToast({
              title: response.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({
            title: '取消失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 处理操作按钮点击
const handleAction = (type: string) => {
  switch (type) {
    case 'confirm':
      confirmOrder()
      break
    case 'cancel':
      cancelOrder()
      break
    case 'contact':
      // 联系客服
      uni.showToast({
        title: '联系客服功能',
        icon: 'none'
      })
      break
    case 'logistics':
      // 查看物流
      uni.showToast({
        title: '查看物流功能',
        icon: 'none'
      })
      break
    case 'reorder':
      // 再次回收
      uni.navigateTo({
        url: '/addon/yz_she/pages/evaluate/index'
      })
      break
  }
}

// 查看订单列表
const viewOrderList = () => {
  uni.navigateTo({
    url: '/addon/yz_she/pages/order/quote-list'
  })
}
</script>

<style lang="scss" scoped>
.quote-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding-bottom: 120rpx;
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #e8f8f5;
      border-top: 4rpx solid #0d7377;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}

// 错误状态
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;
    padding: 40rpx;

    .error-icon {
      font-size: 80rpx;
    }

    .error-text {
      font-size: 28rpx;
      color: #666;
      text-align: center;
    }

    .retry-button {
      padding: 16rpx 32rpx;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 24rpx;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .retry-text {
        font-size: 26rpx;
        color: #fff;
        font-weight: 500;
      }
    }
  }
}

// 顶部卡片容器
.top-card-container {
  margin: 0;

  .top-notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    margin: 0 24rpx;
    border-radius: 16rpx 16rpx 0 0;

    &.evaluating {
      background: linear-gradient(135deg, #ff9500 0%, #ffad33 100%);
    }

    &.pending {
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    }

    &.shipping {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    }

    &.shipped {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
    }

    &.success {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    }

    &.cancelled {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    }

    .notice-text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }

    .notice-right {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
      padding: 8rpx 16rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12rpx;
    }
  }
}

// 订单内容容器
.order-content {
  animation: fadeIn 0.3s ease-in-out;
}

// 顶部状态栏
.status-header {
  padding: 32rpx 24rpx;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;

  &.evaluating {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
  }

  &.completed {
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
  }

  &.pending-ship {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  }

  &.shipped {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  }

  &.received {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
  }

  &.finished {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  }

  &.cancelled {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  }

  .status-content {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .status-icon {
      width: 56rpx;
      height: 56rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 28rpx;
      }
    }

    .status-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4rpx;

      .status-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
      }

      .status-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
      }
    }
  }
}

// 商品信息卡片
.product-card {
  background-color: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .product-info {
    display: flex;
    gap: 24rpx;
    margin-bottom: 24rpx;

    .product-image {
      width: 140rpx;
      height: 140rpx;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

      .product-img {
        width: 100%;
        height: 100%;
      }

      .image-placeholder {
        width: 100%;
        height: 100%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2rpx dashed #ced4da;

        .placeholder-text {
          font-size: 22rpx;
          color: #6c757d;
          font-weight: 500;
        }
      }
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        line-height: 1.4;
      }

      .product-code {
        font-size: 24rpx;
        color: #666;
        font-family: monospace;
        font-weight: 600;
      }

      .order-no {
        font-size: 24rpx;
        color: #0d7377;
        font-weight: 500;
      }
    }
  }

  .price-section {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .price-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .price-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #0d7377;
          font-weight: 600;
        }

        .price-value {
          font-size: 36rpx;
          color: #0d7377;
          font-weight: 700;
        }
      }
    }

    .price-note {
      text-align: center;

      .note-text {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

// 评估照片卡片
.photos-card {
  background-color: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }

    .photo-count {
      font-size: 24rpx;
      color: #0d7377;
      font-weight: 500;
    }
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12rpx;

    .photo-item {
      aspect-ratio: 1;
      border-radius: 8rpx;
      overflow: hidden;
      background-color: #f0f0f0;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .photo-image {
        width: 100%;
        height: 100%;
      }
    }

    .more-photos {
      aspect-ratio: 1;
      background-color: #f8f9fa;
      border: 2rpx dashed #e9ecef;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .more-text {
        font-size: 24rpx;
        color: #999;
        font-weight: 500;
      }
    }
  }
}

// 物流信息卡片
.logistics-card {
  background-color: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .card-header {
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .address-section {
    margin-bottom: 24rpx;

    .section-title {
      margin-bottom: 12rpx;

      .title-text {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }
    }

    .address-info {
      background: linear-gradient(135deg, #f8fffe 0%, #f0f9f8 100%);
      padding: 20rpx;
      border-radius: 12rpx;
      border: 1rpx solid #e8f8f5;
      position: relative;

      .address-name {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .address-detail {
        display: block;
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }

      .copy-btn {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 8rpx 16rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 20rpx;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.95);
        }

        .copy-text {
          font-size: 22rpx;
          color: #fff;
          font-weight: 500;
        }
      }
    }
  }

  .express-section {
    margin-bottom: 32rpx;

    .express-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-label {
        width: 160rpx;

        .label-text {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }
      }

      .item-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .content-text {
          font-size: 26rpx;
          color: #333;

          &.placeholder {
            color: #999;
          }
        }

        .arrow {
          font-size: 24rpx;
          color: #999;
        }

        .tracking-input {
          flex: 1;
          font-size: 26rpx;
          color: #333;
          padding: 0;
          border: none;
          outline: none;
        }
      }
    }
  }

  .ship-button {
    width: 100%;
    padding: 24rpx;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    border-radius: 12rpx;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.3);

    &:active:not(.disabled) {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.4);
    }

    &.disabled {
      background: #f0f0f0;
      box-shadow: none;
      cursor: not-allowed;

      .button-text {
        color: #999;
      }
    }

    .button-text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 600;
    }
  }
}

// 订单进度卡片
.progress-card {
  background-color: #fff;
  margin: 0 20rpx 20rpx 20rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  .card-header {
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .progress-timeline {
    .timeline-item {
      display: flex;
      align-items: flex-start;
      gap: 16rpx;
      padding: 16rpx 0;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 12rpx;
        top: 48rpx;
        width: 2rpx;
        height: calc(100% - 24rpx);
        background: #f0f0f0;
      }

      &.active::after {
        background: #0d7377;
      }

      .step-dot {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50%;
        background: #f0f0f0;
        border: 3rpx solid #fff;
        box-shadow: 0 0 0 2rpx #f0f0f0;
        flex-shrink: 0;
        margin-top: 4rpx;
      }

      &.active .step-dot {
        background: #0d7377;
        box-shadow: 0 0 0 2rpx #0d7377;
      }

      &.current .step-dot {
        background: #ff6b35;
        box-shadow: 0 0 0 2rpx #ff6b35;
        animation: pulse 2s infinite;
      }

      .step-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4rpx;

        .step-title {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .step-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      &.active .step-content .step-title {
        color: #0d7377;
        font-weight: 600;
      }

      &.current .step-content .step-title {
        color: #ff6b35;
        font-weight: 600;
      }
    }
  }
}

// 操作按钮区域
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 24rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 16rpx;

  .action-btn {
    flex: 1;
    padding: 24rpx;
    border-radius: 12rpx;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;

    &:active {
      transform: scale(0.98);
    }

    &.confirm {
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.3);

      .btn-text {
        color: #fff;
        font-size: 26rpx;
        font-weight: 600;
      }
    }

    &.cancel {
      background: linear-gradient(135deg, #fff 0%, #fff8f8 100%);
      border: 2rpx solid #ff4d4f;

      .btn-text {
        color: #ff4d4f;
        font-size: 26rpx;
        font-weight: 500;
      }
    }

    &.contact {
      background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
      border: 2rpx solid #0d7377;

      .btn-text {
        color: #0d7377;
        font-size: 26rpx;
      }
    }

    &.logistics {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);

      .btn-text {
        color: #fff;
        font-size: 26rpx;
      }
    }

    &.reorder {
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);

      .btn-text {
        color: #fff;
        font-size: 26rpx;
      }
    }
  }
}

// 快递公司选择弹窗
.express-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;

  .modal-content {
    width: 100%;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 32rpx 24rpx;
    max-height: 80vh;
    overflow: hidden;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      padding-bottom: 16rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .modal-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        color: #999;
        cursor: pointer;
      }
    }

    .express-list {
      max-height: 60vh;
      overflow-y: auto;

      .express-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:active {
          background-color: #f8fffe;
        }

        &:last-child {
          border-bottom: none;
        }

        .express-name {
          font-size: 26rpx;
          color: #333;
        }

        .express-check {
          font-size: 24rpx;
          color: #0d7377;
          font-weight: 600;
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 107, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 成功页面样式
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx 40rpx;
  text-align: center;

  .success-icon {
    position: relative;
    margin-bottom: 40rpx;

    .clipboard-bg {
      width: 120rpx;
      height: 140rpx;
      background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
      border-radius: 12rpx;
      position: relative;
      box-shadow: 0 8rpx 24rpx rgba(13, 115, 119, 0.3);

      .clipboard-body {
        position: absolute;
        top: 20rpx;
        left: 20rpx;
        right: 20rpx;
        bottom: 20rpx;
        background: #fff;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .clipboard-lines {
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .line {
            width: 40rpx;
            height: 4rpx;
            background: #f0f0f0;
            border-radius: 2rpx;

            &:nth-child(2) {
              width: 60rpx;
            }
          }
        }
      }

      .clipboard-clip {
        position: absolute;
        top: -8rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 16rpx;
        background: #0d7377;
        border-radius: 4rpx;
      }
    }

    .check-mark {
      position: absolute;
      bottom: -12rpx;
      right: -12rpx;
      width: 48rpx;
      height: 48rpx;
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
    }
  }

  .success-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .order-number {
    font-size: 28rpx;
    color: #0d7377;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .success-desc {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 40rpx;
  }

  .view-order-button {
    padding: 20rpx 60rpx;
    background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
    border-radius: 28rpx;
    box-shadow: 0 4rpx 16rpx rgba(13, 115, 119, 0.3);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    .button-text {
      font-size: 28rpx;
      color: #fff;
      font-weight: 500;
    }
  }
}

// 卡片头部样式
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12rpx;
  }

  .card-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }

  .photo-count {
    font-size: 24rpx;
    color: #666;
    background: #f5f5f5;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
  }
}

// 配件信息样式
.accessories-card {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);

  .accessories-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .accessory-item {
      display: flex;
      align-items: center;
      gap: 12rpx;
      padding: 12rpx 16rpx;
      background: #f8fffe;
      border-radius: 12rpx;

      .accessory-icon {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .accessory-name {
        font-size: 26rpx;
        color: #333;
      }
    }
  }
}

// 备注信息样式
.notes-card {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);

  .notes-content {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .note-item {
      padding: 20rpx;
      background: #f8fffe;
      border-radius: 12rpx;
      border-left: 4rpx solid #0d7377;

      .note-label {
        font-size: 24rpx;
        color: #0d7377;
        font-weight: 500;
        margin-bottom: 8rpx;
        display: block;
      }

      .note-text {
        font-size: 26rpx;
        color: #333;
        line-height: 1.5;
      }
    }
  }
}

// 报价信息样式
.quote-card {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(13, 115, 119, 0.08);

  .quote-content {
    .quote-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .quote-label {
        font-size: 26rpx;
        color: #666;
      }

      .quote-value {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .quote-note {
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-top: 20rpx;
      padding: 16rpx;
      background: #fff7e6;
      border-radius: 12rpx;

      .note-text {
        font-size: 24rpx;
        color: #ff6b35;
        line-height: 1.4;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .status-header {
    margin: 0 16rpx 16rpx 16rpx;
    padding: 24rpx 20rpx;
  }

  .product-card,
  .photos-card,
  .logistics-card,
  .progress-card {
    margin: 0 16rpx 16rpx 16rpx;
    padding: 20rpx;
  }

  .action-buttons {
    padding: 16rpx 20rpx;
  }
}

// 估价中状态样式
.evaluating-layout {
  padding: 24rpx;
  background: linear-gradient(180deg, #f8fafe 0%, #f0f4ff 100%);
  min-height: 100vh;

  .status-header {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 20rpx;
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.2);

    .status-icon {
      position: relative;
      width: 64rpx;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .pulse-ring {
        position: absolute;
        width: 64rpx;
        height: 64rpx;
        border: 2rpx solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        animation: pulse 2s infinite;

        &.delay-1 {
          animation-delay: 0.5s;
        }

        &.delay-2 {
          animation-delay: 1s;
        }
      }

      .status-emoji {
        font-size: 32rpx;
        z-index: 1;
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
        display: block;
      }

      .status-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
        margin-bottom: 8rpx;
        display: block;
      }

      .status-time {
        color: rgba(255, 255, 255, 0.9);
        font-size: 22rpx;
        background: rgba(255, 255, 255, 0.1);
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;
      }
    }
  }

  .comprehensive-card {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 24rpx;

    .product-section {
      padding: 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .section-title {
          font-size: 26rpx;
          font-weight: bold;
          color: #333;
        }

        .order-number {
          display: flex;
          align-items: center;
          gap: 8rpx;

          .order-label {
            font-size: 22rpx;
            color: #666;
          }

          .order-value {
            font-size: 22rpx;
            color: #6366f1;
            font-weight: 600;
            background: #f0f4ff;
            padding: 4rpx 8rpx;
            border-radius: 8rpx;
          }
        }
      }

      .product-main {
        display: flex;
        gap: 20rpx;

        .product-image-wrapper {
          position: relative;

          .product-image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 12rpx;
          }

          .image-placeholder {
            width: 120rpx;
            height: 120rpx;
            background: #f8f9fa;
            border-radius: 12rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 2rpx dashed #ddd;

            .placeholder-icon {
              font-size: 32rpx;
              margin-bottom: 4rpx;
            }

            .placeholder-text {
              font-size: 20rpx;
              color: #999;
            }
          }

          .evaluation-badge {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            padding: 4rpx 8rpx;
            border-radius: 8rpx;

            .badge-text {
              color: #fff;
              font-size: 18rpx;
              font-weight: 600;
            }
          }
        }

        .product-info {
          flex: 1;

          .product-name {
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
            display: block;
          }

          .product-model {
            font-size: 22rpx;
            color: #666;
            margin-bottom: 16rpx;
            display: block;
          }

          .price-section {
            .price-label {
              font-size: 22rpx;
              color: #666;
              margin-bottom: 8rpx;
              display: block;
            }

            .price-display {
              display: flex;
              align-items: baseline;
              gap: 4rpx;

              .currency {
                font-size: 24rpx;
                color: #6366f1;
                font-weight: 600;
              }

              .price-amount {
                font-size: 32rpx;
                color: #6366f1;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .photos-section {
      padding: 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .section-title {
          font-size: 26rpx;
          font-weight: bold;
          color: #333;
        }

        .photo-count {
          font-size: 22rpx;
          color: #6366f1;
          background: #f0f4ff;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }

      .photos-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;

        .photo-item {
          width: 100rpx;
          height: 100rpx;
          border-radius: 8rpx;
          overflow: hidden;

          .photo-image {
            width: 100%;
            height: 100%;
          }
        }

        .more-photos {
          width: 100rpx;
          height: 100rpx;
          background: #f8f9fa;
          border-radius: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2rpx dashed #ddd;

          .more-text {
            font-size: 22rpx;
            color: #666;
            font-weight: 600;
          }
        }
      }
    }

    .accessories-section {
      padding: 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .section-header {
        margin-bottom: 16rpx;

        .section-title {
          font-size: 26rpx;
          font-weight: bold;
          color: #333;
        }
      }

      .accessories-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;

        .accessory-item {
          display: flex;
          align-items: center;
          gap: 8rpx;
          background: #f0f9ff;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;

          .accessory-icon {
            color: #10b981;
            font-size: 18rpx;
            font-weight: bold;
          }

          .accessory-name {
            font-size: 22rpx;
            color: #333;
          }
        }
      }
    }

    .notes-section {
      padding: 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .section-header {
        margin-bottom: 16rpx;

        .section-title {
          font-size: 26rpx;
          font-weight: bold;
          color: #333;
        }
      }

      .notes-content {
        .note-item {
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .note-label {
            font-size: 22rpx;
            color: #666;
            margin-bottom: 4rpx;
            display: block;
          }

          .note-text {
            font-size: 24rpx;
            color: #333;
            line-height: 1.5;
            background: #f8f9fa;
            padding: 12rpx;
            border-radius: 8rpx;
            display: block;
          }
        }
      }
    }

    .tips-section {
      padding: 24rpx;
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      display: flex;
      gap: 12rpx;

      .tips-icon {
        font-size: 24rpx;
        margin-top: 2rpx;
      }

      .tips-content {
        flex: 1;

        .tips-title {
          font-size: 24rpx;
          font-weight: bold;
          color: #92400e;
          margin-bottom: 8rpx;
          display: block;
        }

        .tips-text {
          font-size: 22rpx;
          color: #92400e;
          line-height: 1.5;
        }
      }
    }
  }

  .progress-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .progress-title {
        font-size: 26rpx;
        font-weight: bold;
        color: #333;
      }

      .progress-percentage {
        font-size: 24rpx;
        color: #6366f1;
        font-weight: bold;
      }
    }

    .progress-bar-container {
      margin-bottom: 24rpx;

      .progress-bar {
        width: 100%;
        height: 8rpx;
        background: #f0f0f0;
        border-radius: 4rpx;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
          border-radius: 4rpx;
          transition: width 0.3s ease;
        }
      }
    }

    .progress-steps {
      display: flex;
      justify-content: space-between;

      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;
        flex: 1;

        .step-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background: #e5e7eb;
          transition: all 0.3s ease;
        }

        .step-label {
          font-size: 20rpx;
          color: #9ca3af;
          text-align: center;
          transition: color 0.3s ease;
        }

        &.active {
          .step-dot {
            background: #6366f1;
            transform: scale(1.2);
          }

          .step-label {
            color: #6366f1;
            font-weight: 600;
          }
        }

        &.completed {
          .step-dot {
            background: #10b981;
          }

          .step-label {
            color: #10b981;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
</style>
