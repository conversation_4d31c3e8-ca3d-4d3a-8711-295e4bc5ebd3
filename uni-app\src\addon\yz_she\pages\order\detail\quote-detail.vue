<template>
  <view class="quote-detail-page">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 订单内容 -->
    <view v-else-if="orderInfo.id" class="order-content">

      <!-- 已完成状态 - 成功页面布局 -->
      <template v-if="orderInfo.status === 5">
        <view class="success-layout">
          <!-- 商品信息卡片 -->
          <view class="product-card">
            <view class="product-info">
              <image
                v-if="orderInfo.product_image"
                :src="img(orderInfo.product_image)"
                class="product-image"
                mode="aspectFit"
              ></image>
              <view v-else class="product-image no-image">
                <u-icon name="image" color="#ccc" size="40"></u-icon>
              </view>
              <view class="product-details">
                <text class="product-name">{{ orderInfo.product_name || '估价商品' }}</text>
                <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
              </view>
            </view>

            <view class="price-section">
              <view class="price-info">
                <text class="price-label">实际回收</text>
                <view class="price-amount">
                  <text class="currency">¥</text>
                  <text class="price-value">{{ orderInfo.quote_price?.toFixed(2) || '0.00' }}</text>
                </view>
              </view>
              <view class="quote-note">
                <text class="note-text">交易已完成，感谢您的信任</text>
              </view>
            </view>

            <!-- 用户上传的图片 -->
            <view class="upload-photos" v-if="userPhotos.length > 0">
              <text class="photos-title">上传图片</text>
              <scroll-view class="photos-scroll" scroll-x="true" show-scrollbar="false">
                <view class="photos-list">
                  <view class="photo-item" v-for="photo in userPhotos" :key="photo.id">
                    <image :src="photo.photo_url" class="photo-image" mode="aspectFill" @click="previewPhoto(photo.photo_url)"></image>
                    <text class="photo-label">{{ photo.photo_name }}</text>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>

          <!-- 成功状态区域 -->
          <view class="success-section">
            <view class="success-icon">
              <view class="quote-bg">
                <view class="quote-body">
                  <view class="quote-lines">
                    <view class="line"></view>
                    <view class="line"></view>
                    <view class="line"></view>
                  </view>
                </view>
                <view class="quote-clip"></view>
              </view>
              <view class="check-mark">
                <u-icon name="checkmark" color="#fff" size="16"></u-icon>
              </view>
            </view>

            <text class="success-title">交易完成</text>
            <text class="order-number">估价单号: {{ orderInfo.order_no }}</text>
            <text class="success-desc">感谢您选择我们的回收服务</text>
          </view>
        </view>
      </template>

      <!-- 待发货状态 - 批量提交页面布局 -->
      <template v-else-if="orderInfo.status === 3">
        <view class="batch-layout">
          <!-- 顶部提示栏 -->
          <view class="top-notice">
            <text class="notice-text">请选择商品成色并提交回收</text>
            <text class="notice-right">待发货</text>
          </view>

          <!-- 商品信息卡片 -->
          <view class="product-card">
            <view class="product-info">
              <image
                v-if="orderInfo.product_image"
                :src="img(orderInfo.product_image)"
                class="product-image"
                mode="aspectFit"
              ></image>
              <view v-else class="product-image no-image">
                <u-icon name="image" color="#ccc" size="40"></u-icon>
              </view>
              <view class="product-details">
                <text class="product-name">{{ orderInfo.product_name || '估价商品' }}</text>
                <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
              </view>
            </view>

            <!-- 价格选项 -->
            <view class="price-options">
              <view
                class="price-option"
                :class="{ active: selectedCondition === 'new' }"
                @click="selectCondition('new')"
              >
                <text class="option-label">全新未使用</text>
                <text class="option-price">¥{{ conditionPrices.new }}</text>
              </view>
              <view
                class="price-option"
                :class="{ active: selectedCondition === 'good' }"
                @click="selectCondition('good')"
              >
                <text class="option-label">轻微使用</text>
                <text class="option-price">¥{{ conditionPrices.good }}</text>
              </view>
              <view
                class="price-option"
                :class="{ active: selectedCondition === 'fair' }"
                @click="selectCondition('fair')"
              >
                <text class="option-label">明显使用</text>
                <text class="option-price">¥{{ conditionPrices.fair }}</text>
              </view>
            </view>
          </view>

          <!-- 提交按钮 -->
          <view class="submit-section">
            <view class="submit-button" :class="{ disabled: !selectedCondition }" @click="submitRecycle">
              <text class="submit-text">提交回收</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 其他状态 - 订单详情页面布局 -->
      <template v-else>
        <view class="detail-layout">
          <!-- 订单状态卡片 -->
          <view class="status-card">
            <view class="status-header">
              <view class="status-icon" :class="getStatusIconClass(orderInfo.status)">
                <u-icon :name="getStatusIcon(orderInfo.status)" color="#fff" size="24"></u-icon>
              </view>
              <view class="status-info">
                <text class="status-title">{{ getStatusText(orderInfo.status) }}</text>
                <text class="status-desc">{{ getStatusDesc(orderInfo.status) }}</text>
              </view>
            </view>
          </view>

          <!-- 商品信息卡片 -->
          <view class="product-card">
            <view class="card-header">
              <text class="card-title">商品信息</text>
              <text class="order-no">{{ orderInfo.order_no }}</text>
            </view>

            <view class="product-info">
              <image
                v-if="orderInfo.product_image"
                :src="img(orderInfo.product_image)"
                class="product-image"
                mode="aspectFit"
              ></image>
              <view v-else class="product-image no-image">
                <u-icon name="image" color="#ccc" size="40"></u-icon>
              </view>

              <view class="product-details">
                <text class="product-name">{{ orderInfo.product_name || '估价商品' }}</text>
                <text class="product-code" v-if="orderInfo.product_code">{{ orderInfo.product_code }}</text>
                <view class="product-meta">
                  <text class="meta-item">品牌: {{ orderInfo.brand?.name || '未知品牌' }}</text>
                  <text class="meta-item">分类: {{ orderInfo.category?.name || '未知分类' }}</text>
                </view>
              </view>

              <view class="price-info">
                <text class="price-label">{{ getPriceLabel(orderInfo.status) }}</text>
                <view class="price-amount">
                  <text v-if="getCurrentPrice(orderInfo) !== '估价中'" class="currency">¥</text>
                  <text class="price-value">{{ getCurrentPrice(orderInfo) }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 估价信息卡片 -->
          <view class="quote-info-card">
            <view class="card-header">
              <text class="card-title">估价信息</text>
            </view>

            <view class="quote-details">
              <view class="quote-item">
                <text class="quote-label">创建时间</text>
                <text class="quote-value">{{ formatTime(orderInfo.create_time) }}</text>
              </view>
              <view class="quote-item" v-if="orderInfo.quote_time">
                <text class="quote-label">估价时间</text>
                <text class="quote-value">{{ formatTime(orderInfo.quote_time) }}</text>
              </view>
              <view class="quote-item" v-if="orderInfo.admin_note">
                <text class="quote-label">估价说明</text>
                <text class="quote-value note">{{ orderInfo.admin_note }}</text>
              </view>
              <view class="quote-item" v-if="orderInfo.user_note">
                <text class="quote-label">用户备注</text>
                <text class="quote-value note">{{ orderInfo.user_note }}</text>
              </view>
            </view>
          </view>

          <!-- 用户上传信息 -->
          <view class="upload-info-card" v-if="userPhotos.length > 0">
            <view class="card-header">
              <text class="card-title">上传信息</text>
            </view>

            <view class="upload-photos">
              <text class="photos-title">上传图片</text>
              <scroll-view class="photos-scroll" scroll-x="true" show-scrollbar="false">
                <view class="photos-list">
                  <view class="photo-item" v-for="photo in userPhotos" :key="photo.id">
                    <image :src="photo.photo_url" class="photo-image" mode="aspectFill" @click="previewPhoto(photo.photo_url)"></image>
                    <text class="photo-label">{{ photo.photo_name }}</text>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons" v-if="hasActions">
            <view
              class="action-btn"
              :class="action.type"
              v-for="action in getOrderActions(orderInfo.status)"
              :key="action.type"
              @click="handleAction(action.type)"
            >
              <text class="action-text">{{ action.text }}</text>
            </view>
          </view>
        </view>
      </template>

    </view>

    <!-- 错误状态 -->
    <view v-else class="error-container">
      <view class="error-content">
        <u-icon name="close-circle" color="#ff4d4f" size="64"></u-icon>
        <text class="error-text">订单信息加载失败</text>
        <view class="error-button" @click="loadOrderDetail">
          <text class="error-button-text">重新加载</text>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderDetail, confirmQuoteOrder, cancelQuoteOrder, submitRecycleOrder } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 页面参数
const orderId = ref('')
const loading = ref(false)

// 订单信息
const orderInfo = ref<any>({
  id: 0,
  order_no: '',
  status: 1,
  product_name: '',
  product_code: '',
  product_image: '',
  quote_price: null,
  user_note: '',
  admin_note: '',
  create_time: '',
  quote_time: '',
  brand: null,
  category: null
})

// 用户上传的图片
const userPhotos = ref<any[]>([])

// 评估信息
const evaluateInfo = ref({
  photoCount: 0,
  photos: []
})

// 物流信息
const selectedExpress = ref('')
const trackingNumber = ref('')
const showExpressSelect = ref(false)

// 快递公司列表
const expressList = [
  '顺丰速运',
  '京东物流',
  '德邦快递',
  '中通快递',
  '韵达速递',
  '圆通速递',
  '申通快递',
  '极兔速递'
]

// 订单步骤
const orderSteps = computed(() => {
  const steps = [
    { title: '订单创建', completed: true, time: formatTime(orderInfo.value.create_time) },
    { title: '评估完成', completed: orderInfo.value.status >= 2, time: formatTime(orderInfo.value.quote_time) },
    { title: '用户确认', completed: orderInfo.value.status >= 3, time: formatTime(orderInfo.value.confirm_time) },
    { title: '商品发货', completed: orderInfo.value.status >= 4, time: formatTime(orderInfo.value.ship_time) },
    { title: '交易完成', completed: orderInfo.value.status >= 5, time: formatTime(orderInfo.value.complete_time) }
  ]

  // 标记当前步骤
  const currentIndex = steps.findIndex(step => !step.completed)
  if (currentIndex >= 0 && currentIndex > 0) {
    steps[currentIndex - 1].current = true
  } else if (currentIndex === -1 && orderInfo.value.status < 6) {
    // 所有步骤都完成但状态不是已取消，标记最后一个为当前
    steps[steps.length - 1].current = true
  }

  return steps
})

// 是否可以发货
const canShip = computed(() => {
  return selectedExpress.value && trackingNumber.value.trim()
})

// 是否有操作按钮
const hasActions = computed(() => {
  if (!orderInfo.value.id) return false
  const actions = getOrderActions(orderInfo.value.status)
  return actions.length > 0
})

// 获取状态样式类
const getStatusClass = (status: number) => {
  const statusMap = {
    1: 'evaluating',    // 评估中
    2: 'completed',     // 待确认
    3: 'pending-ship',  // 待发货
    4: 'shipped',       // 已发货
    5: 'finished',      // 已完成
    6: 'cancelled'      // 已取消
  }
  return statusMap[status] || 'evaluating'
}

// 获取状态图标
const getStatusIcon = (status: number) => {
  const iconMap = {
    1: '⏳',
    2: '✅',
    3: '📦',
    4: '🚚',
    5: '🎉',
    6: '❌'
  }
  return iconMap[status] || '⏳'
}

// 获取状态标题
const getStatusTitle = (status: number) => {
  const titleMap = {
    1: '评估中',
    2: '待确认',
    3: '待发货',
    4: '已发货',
    5: '已完成',
    6: '已取消'
  }
  return titleMap[status] || '评估中'
}

// 获取状态描述
const getStatusDesc = (status: number) => {
  const descMap = {
    1: '专业评估师正在评估中，请耐心等待',
    2: '评估已完成，请确认价格',
    3: '请填写物流信息并发货',
    4: '商品已发货，等待平台收货',
    5: '交易已完成，感谢您的信任',
    6: '订单已取消'
  }
  return descMap[status] || '评估中'
}

// 获取状态提示文字
const getStatusNotice = (status: number) => {
  const noticeMap = {
    1: '评估中 专业评估师正在评估',
    2: '评估完成 请确认价格',
    3: '待发货 请填写物流信息',
    4: '已发货 商品运输中',
    5: '已完成 交易成功',
    6: '已取消 订单已取消'
  }
  return noticeMap[status] || '评估中 专业评估师正在评估'
}

// 获取价格标签
const getPriceLabel = (status: number) => {
  if (status === 1) return '预估价格'
  if (status === 6) return '取消价格'
  return '回收价格'
}

// 获取订单操作
const getOrderActions = (status: number) => {
  const actionsMap = {
    1: [{ type: 'cancel', text: '取消订单' }], // 评估中
    2: [
      { type: 'confirm', text: '确认回收' },
      { type: 'cancel', text: '取消订单' }
    ], // 待确认
    3: [], // 待发货（在卡片内操作）
    4: [{ type: 'logistics', text: '查看物流' }], // 已发货
    5: [{ type: 'reorder', text: '再次回收' }], // 已完成
    6: [{ type: 'reorder', text: '再次回收' }] // 已取消
  }
  return actionsMap[status] || []
}

// 页面加载
onLoad((options) => {
  if (options.id) {
    orderId.value = options.id
    loadOrderDetail()
  }
  if (options.status) {
    orderInfo.value.status = parseInt(options.status)
  }
})

// 加载订单详情
const loadOrderDetail = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const response = await getQuoteOrderDetail(parseInt(orderId.value))
    if (response.code === 1) {
      const data = response.data
      orderInfo.value = {
        ...data,
        photos: data.photos || [],
        accessories: data.accessories || []
      }

      // 处理评估照片
      if (data.photos && data.photos.length > 0) {
        evaluateInfo.value.photos = data.photos.map(photo => img(photo.photo_url))
        evaluateInfo.value.photoCount = data.photos.length
      }
    } else {
      uni.showToast({
        title: response.msg || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 预览照片
const previewPhoto = (index: number) => {
  uni.previewImage({
    urls: evaluateInfo.value.photos,
    current: index
  })
}

// 复制地址
const copyAddress = () => {
  const address = '放心星仓库 13060000687 四川省成都市龙泉驿区青羊镇街道龙井3组远洋快递'
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      })
    }
  })
}

// 显示快递选择弹窗
const showExpressModal = () => {
  showExpressSelect.value = true
}

// 隐藏快递选择弹窗
const hideExpressModal = () => {
  showExpressSelect.value = false
}

// 选择快递公司
const selectExpress = (express: string) => {
  selectedExpress.value = express
  showExpressSelect.value = false
  uni.showToast({
    title: `已选择${express}`,
    icon: 'success'
  })
}

// 提交发货信息
const submitShipping = () => {
  if (!canShip.value) {
    uni.showToast({
      title: '请完善物流信息',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '确认发货',
    content: `快递公司：${selectedExpress.value}\n快递单号：${trackingNumber.value}`,
    success: (res) => {
      if (res.confirm) {
        // 提交发货信息
        console.log('提交发货信息')
        uni.showToast({
          title: '发货成功',
          icon: 'success'
        })
        // 更新订单状态
        orderInfo.value.status = 4
        orderInfo.value.ship_time = new Date().toLocaleString()
      }
    }
  })
}

// 确认订单
const confirmOrder = async () => {
  uni.showModal({
    title: '确认回收',
    content: `确认以¥${orderInfo.value.quote_price}的价格回收此商品？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await confirmQuoteOrder(parseInt(orderId.value))
          if (response.code === 1) {
            uni.showToast({
              title: '确认成功，请寄出商品',
              icon: 'success'
            })
            // 重新加载订单信息
            loadOrderDetail()
          } else {
            uni.showToast({
              title: response.msg || '确认失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('确认订单失败:', error)
          uni.showToast({
            title: '确认失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 取消订单
const cancelOrder = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await cancelQuoteOrder(parseInt(orderId.value), '用户主动取消')
          if (response.code === 1) {
            uni.showToast({
              title: '取消成功',
              icon: 'success'
            })
            // 重新加载订单信息
            loadOrderDetail()
          } else {
            uni.showToast({
              title: response.msg || '取消失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({
            title: '取消失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 处理操作按钮点击
const handleAction = (type: string) => {
  switch (type) {
    case 'confirm':
      confirmOrder()
      break
    case 'cancel':
      cancelOrder()
      break
    case 'contact':
      // 联系客服
      uni.showToast({
        title: '联系客服功能',
        icon: 'none'
      })
      break
    case 'logistics':
      // 查看物流
      uni.showToast({
        title: '查看物流功能',
        icon: 'none'
      })
      break
    case 'reorder':
      // 再次回收
      uni.navigateTo({
        url: '/addon/yz_she/pages/evaluate/index'
      })
      break
  }
}

// 查看订单列表
const viewOrderList = () => {
  uni.navigateTo({
    url: '/addon/yz_she/pages/order/quote-list'
  })
}
</script>

<style lang="scss" scoped>
.quote-detail-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f0f0f0;
      border-top: 4rpx solid #16a085;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 26rpx;
      color: #666;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 错误状态
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;

  .error-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
    padding: 40rpx;

    .error-text {
      font-size: 28rpx;
      color: #666;
      margin-top: 16rpx;
    }

    .error-button {
      margin-top: 20rpx;
      padding: 12rpx 32rpx;
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      border-radius: 32rpx;
      color: #fff;

      .error-button-text {
        font-size: 26rpx;
        font-weight: 600;
      }
    }
  }
}
</style>
