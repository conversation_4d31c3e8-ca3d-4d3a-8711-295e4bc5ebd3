<template>
  <view class="order-list-page">
    <!-- 顶部状态筛选 -->
    <scroll-view class="status-tabs-container" scroll-x="true" show-scrollbar="false">
      <view class="status-tabs">
        <view
          class="tab-item"
          :class="{ active: currentStatus === item.value }"
          v-for="item in statusTabs"
          :key="item.value"
          @click="switchStatus(item.value)"
        >
          <text class="tab-text">{{ item.label }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 订单列表 -->
    <scroll-view
      class="order-list"
      scroll-y="true"
      refresher-enabled="true"
      :refresher-triggered="loading"
      refresher-threshold="100"
      refresher-default-style="black"
      @refresherrefresh="onRefresh"
      @refresherpulling="onRefresherPulling"
      @refresherrestore="onRefresherRestore"
      @scrolltolower="loadMore"
      @scroll="onScroll"
      lower-threshold="100"
    >
      <view
        class="order-item"
        v-for="order in filteredOrders"
        :key="order.id || order.orderNo"
        @click="viewOrderDetail(order)"
      >
        <view class="order-content">
          <view class="order-header">
            <view class="order-header-left">
              <text class="order-no">订单号: {{ order.orderNo }}</text>
            </view>
            <view class="order-status" :class="getStatusClass(order.status)">
              <text class="status-text">{{ getStatusText(order.status) }}</text>
            </view>
          </view>

          <view class="order-time">
            <text class="time-text">创建时间: {{ order.createTime }}</text>
          </view>

          <view class="product-info">
            <!-- 有商品图片时显示图片 -->
            <image
              v-if="order.productImage"
              :src="order.productImage"
              class="product-image"
              mode="aspectFit"
            ></image>
            <!-- 批量回收订单显示图标 -->
            <view
              v-else
              class="product-image batch-icon-container"
            >
              <u-icon name="shopping-cart-fill" color="#16a085" size="40"></u-icon>
            </view>
            <view class="product-details">
              <text class="product-name">{{ order.productName }}</text>
              <text class="product-code" v-if="order.productCode">{{ order.productCode }}</text>
              <text class="batch-label" v-else>批量回收商品</text>
            </view>
            <view class="price-info">
              <text class="price-label">{{ getPriceLabel(order.status) }}</text>
              <view class="price-amount">
                <text class="currency">¥</text>
                <text class="price-value">{{ order.price }}</text>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="order-actions" v-if="hasActions(order.status)">
            <view
              class="action-btn"
              v-for="action in getOrderActions(order.status)"
              :key="action.type"
              :class="action.type"
              @click.stop="handleAction(order, action.type)"
            >
              <text class="action-text">{{ action.text }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredOrders.length === 0 && !loading">
        <view class="empty-icon">
          <u-icon name="shopping-cart" color="#ccc" size="80"></u-icon>
        </view>
        <text class="empty-text">暂无{{ getStatusText(currentStatus) }}订单</text>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more" v-if="filteredOrders.length > 0">
        <view v-if="loading" class="loading-text">
          <text>加载中...</text>
        </view>
        <view v-else-if="!hasMore" class="no-more-text">
          <text>没有更多数据了</text>
        </view>
        <view v-else class="pull-up-text">
          <text>上拉加载更多</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部加载更多 -->
    <view class="load-more" v-if="hasMore && filteredOrders.length > 0">
      <text class="load-text">加载更多...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getRecycleOrderList, cancelRecycleOrder, requestReturn, confirmPriceAndSettle } from '@/addon/yz_she/api/recycle_order'
import { img } from '@/utils/common'

// 状态标签页 (使用数字状态值对应后端)
const statusTabs = ref([
  { label: '全部', value: 'all' },
  { label: '待取件', value: 1 },
  { label: '待收货', value: 2 },
  { label: '待质检', value: 3 },
  { label: '待确认', value: 4 },
  { label: '待退回', value: 5 },
  { label: '已退回', value: 6 },
  { label: '已完成', value: 7 },
  { label: '已取消', value: 8 }
])

// 当前选中状态
const currentStatus = ref('all')

// 分页参数
const page = ref(1)
const limit = ref(10)
const total = ref(0)
const loading = ref(false)
const hasMore = ref(true)

// 订单数据
const orders = ref([])

// 过滤后的订单列表
const filteredOrders = computed(() => {
  if (currentStatus.value === 'all') {
    return orders.value
  }
  return orders.value.filter(order => order.status === currentStatus.value)
})

// 切换状态
const switchStatus = (status: string | number) => {
  console.log('切换状态:', status)

  // 添加触觉反馈
  uni.vibrateShort({
    type: 'light'
  })

  // 如果是相同状态，不需要切换
  if (currentStatus.value === status) {
    return
  }

  currentStatus.value = status

  // 显示切换提示
  const statusText = getStatusText(status)
  uni.showToast({
    title: `切换到${statusText}`,
    icon: 'none',
    duration: 1000
  })

  // 重置分页并重新加载数据
  page.value = 1
  orders.value = []
  loadOrderList()
}

// 加载订单列表
const loadOrderList = async (isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true

    const params: any = {
      page: page.value,
      limit: limit.value
    }

    // 如果不是全部状态，添加状态筛选
    if (currentStatus.value !== 'all') {
      params.status = currentStatus.value
    }

    console.log('加载订单列表参数:', params)
    const response = await getRecycleOrderList(params)

    if (response.code === 1) {
      const newOrders = response.data.data || []

      // 处理订单数据，添加图片处理
      const processedOrders = newOrders.map(order => ({
        ...order,
        orderNo: order.order_no,
        productName: order.product_name,
        productCode: order.product_code,
        productImage: order.product_image ? img(order.product_image) : '',
        createTime: order.create_time_text,
        price: order.expected_price || 0
      }))

      if (isLoadMore) {
        orders.value = [...orders.value, ...processedOrders]
      } else {
        orders.value = processedOrders
      }

      total.value = response.data.total || 0
      hasMore.value = orders.value.length < total.value

      console.log('订单列表加载成功:', {
        current: orders.value.length,
        total: total.value,
        hasMore: hasMore.value
      })
    } else {
      throw new Error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('加载订单列表失败:', error)
    uni.showToast({
      title: error.message || '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    page.value++
    loadOrderList(true)
  }
}

// 下拉刷新
const onRefresh = () => {
  page.value = 1
  orders.value = []
  loadOrderList()
}

// 获取状态样式类
const getStatusClass = (status: number) => {
  return `status-${status}`
}

// 获取状态文字
const getStatusText = (status: number | string) => {
  if (status === 'all') return '全部'

  const statusMap = {
    1: '待取件',
    2: '待收货',
    3: '待质检',
    4: '待确认',
    5: '待退回',
    6: '已退回',
    7: '已完成',
    8: '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 获取价格标签
const getPriceLabel = (status: number) => {
  const labelMap = {
    1: '预估价格',  // 待取件
    2: '预估价格',  // 待收货
    3: '预估价格',  // 待质检
    4: '质检价格',  // 待确认
    5: '质检价格',  // 待退回
    6: '退回价格',  // 已退回
    7: '成交价格',  // 已完成
    8: '预估价格'   // 已取消
  }
  return labelMap[status] || '预估价格'
}

// 判断是否有操作按钮
const hasActions = (status: number) => {
  return [1, 4, 6].includes(status) // 待取件、待确认、已退回
}

// 获取订单操作按钮
const getOrderActions = (status: number) => {
  const actionsMap = {
    1: [  // 待取件
      { type: 'cancel', text: '取消订单' },
      { type: 'contact', text: '联系客服' }
    ],
    4: [  // 待确认
      { type: 'reject', text: '拒绝价格' },
      { type: 'accept', text: '接受价格' }
    ],
    6: [  // 已退回
      { type: 'logistics', text: '查看物流' }
    ]
  }
  return actionsMap[status] || []
}

// 处理操作按钮点击
const handleAction = (order: any, actionType: string) => {
  console.log('处理操作:', order.orderNo, actionType)

  // 添加触觉反馈
  uni.vibrateShort({
    type: 'light'
  })

  switch (actionType) {
    case 'cancel':
      cancelOrder(order)
      break
    case 'contact':
      contactService(order)
      break
    case 'reject':
      rejectPrice(order)
      break
    case 'accept':
      acceptPrice(order)
      break
    case 'logistics':
      viewLogistics(order)
      break
    case 'detail':
      viewOrderDetail(order)
      break
    default:
      console.log('未知操作类型:', actionType)
      uni.showToast({
        title: '功能暂未开放',
        icon: 'none'
      })
  }
}

// 取消订单
const cancelOrder = async (order: any) => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这个订单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await cancelRecycleOrder(order.id, '用户主动取消')

          if (response.code === 1) {
            // 更新本地数据
            const orderIndex = orders.value.findIndex(item => item.id === order.id)
            if (orderIndex !== -1) {
              orders.value[orderIndex].status = 8 // 已取消
            }

            uni.showToast({
              title: '订单已取消',
              icon: 'success'
            })
          } else {
            throw new Error(response.msg || '取消订单失败')
          }
        } catch (error) {
          console.error('取消订单失败:', error)
          uni.showToast({
            title: error.message || '取消失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 联系客服
const contactService = (order: any) => {
  uni.showToast({
    title: '正在为您转接客服',
    icon: 'none'
  })
}

// 拒绝价格
const rejectPrice = async (order: any) => {
  uni.showModal({
    title: '拒绝价格',
    content: `确定要拒绝¥${order.price}的价格吗？商品将退回给您。`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await requestReturn(order.id, '用户拒绝质检价格')

          if (response.code === 1) {
            // 更新本地数据
            const orderIndex = orders.value.findIndex(item => item.id === order.id)
            if (orderIndex !== -1) {
              orders.value[orderIndex].status = 5 // 待退回
            }

            uni.showToast({
              title: '已拒绝价格，商品将退回',
              icon: 'success'
            })
          } else {
            throw new Error(response.msg || '申请退回失败')
          }
        } catch (error) {
          console.error('拒绝价格失败:', error)
          uni.showToast({
            title: error.message || '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 接受价格
const acceptPrice = async (order: any) => {
  uni.showModal({
    title: '接受价格',
    content: `确定接受¥${order.price}的价格吗？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await confirmPriceAndSettle(order.id)

          if (response.code === 1) {
            // 更新本地数据
            const orderIndex = orders.value.findIndex(item => item.id === order.id)
            if (orderIndex !== -1) {
              orders.value[orderIndex].status = 7 // 已完成
            }

            uni.showToast({
              title: '价格已确认，交易完成',
              icon: 'success'
            })
          } else {
            throw new Error(response.msg || '确认价格失败')
          }
        } catch (error) {
          console.error('确认价格失败:', error)
          uni.showToast({
            title: error.message || '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 查看物流
const viewLogistics = (order: any) => {
  uni.navigateTo({
    url: `/addon/yz_she/pages/logistics/detail?orderNo=${order.orderNo}`
  })
}

// 查看订单详情
const viewOrderDetail = (order: any) => {
  console.log('跳转到订单详情:', order)

  // 使用订单ID跳转到订单详情页面
  const orderId = order.id || order.orderNo
  if (!orderId) {
    uni.showToast({
      title: '订单信息错误',
      icon: 'none'
    })
    return
  }

  // 显示加载提示
  uni.showLoading({
    title: '加载中...',
    mask: true
  })

  // 延迟一下再跳转，给用户反馈
  setTimeout(() => {
    uni.hideLoading()

    uni.navigateTo({
      url: `/addon/yz_she/pages/order/detail/order-detail?id=${orderId}`,
      success: () => {
        console.log('跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  }, 300)
}

// 下拉刷新拉动中
const onRefresherPulling = (e: any) => {
  console.log('下拉刷新拉动中:', e.detail)
}

// 下拉刷新恢复
const onRefresherRestore = () => {
  console.log('下拉刷新恢复')
}

// 滚动事件
const onScroll = (e: any) => {
  const { scrollTop, scrollHeight, scrollViewHeight } = e.detail

  // 当滚动到接近底部时，预加载更多数据
  if (scrollTop + scrollViewHeight >= scrollHeight - 200) {
    if (hasMore.value && !loading.value) {
      loadMore()
    }
  }
}

// 页面加载
onMounted(() => {
  console.log('订单列表页面加载完成')
  loadOrderList()
})
</script>

<style lang="scss" scoped>
.order-list-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  width: 100%;
  overflow-x: hidden; // 防止水平滚动
}

// 状态标签页容器 (优化全部标签位置)
.status-tabs-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  white-space: nowrap;
}

.status-tabs {
  display: flex;
  padding: 0 16rpx; // 减少左边距，让全部标签更靠左

  .tab-item {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 20rpx;
    position: relative;
    min-width: 100rpx;

    .tab-text {
      font-size: 28rpx;
      color: #666;
      transition: color 0.3s ease;
      white-space: nowrap;
    }

    &.active {
      .tab-text {
        color: #16a085;
        font-weight: 600;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: linear-gradient(90deg, #16a085, #1abc9c);
        border-radius: 2rpx;
        box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.4);
      }
    }
  }
}

// 订单列表
.order-list {
  padding: 20rpx 16rpx;

  .order-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
      transform: translateY(-2rpx);
    }

    &:active {
      transform: scale(0.98) translateY(0);
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
    }

    .order-content {
      padding: 32rpx 24rpx;
    }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      .order-header-left {
        display: flex;
        align-items: center;
        gap: 12rpx;
      }

      .order-no {
        font-size: 26rpx;
        color: #333;
        font-weight: 600;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;

        .status-text {
          font-weight: 500;
        }

        // 订单状态颜色 - 使用数字状态值
        &.status-1 {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-2 {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &.status-3 {
          background-color: #fff1f0;
          color: #ff4d4f;
        }

        &.status-4 {
          background-color: #f0fffe;
          color: #16a085;
        }

        &.status-5 {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-6 {
          background-color: #f5f5f5;
          color: #8c8c8c;
        }

        &.status-7 {
          background-color: #f6ffed;
          color: #52c41a;
        }

        &.status-8 {
          background-color: #f5f5f5;
          color: #999;
        }
      }
    }

    .order-time {
      margin-bottom: 20rpx;

      .time-text {
        font-size: 24rpx;
        color: #999;
      }
    }

    .product-info {
      display: flex;
      align-items: center;
      gap: 20rpx;
      margin-bottom: 24rpx;

      .product-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        background-color: #f0f0f0;

        &.batch-icon-container {
          background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
          border: 2rpx solid rgba(22, 160, 133, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.1);
        }
      }

      .product-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8rpx;

        .product-name {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .product-code {
          font-size: 22rpx;
          color: #999;
        }

        .batch-label {
          font-size: 22rpx;
          color: #16a085;
          font-weight: 500;
        }
      }

      .price-info {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4rpx;

        .price-label {
          font-size: 22rpx;
          color: #666;
        }

        .price-amount {
          display: flex;
          align-items: baseline;
          gap: 2rpx;

          .currency {
            font-size: 22rpx;
            color: #16a085;
            font-weight: 600;
          }

          .price-value {
            font-size: 32rpx;
            color: #16a085;
            font-weight: 700;
          }
        }
      }
    }

    // 操作按钮
    .order-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16rpx;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 20rpx;

      .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &.cancel {
          background-color: #fff;
          border: 1rpx solid #d9d9d9;
          color: #666;

          &:active {
            background-color: #f5f5f5;
          }
        }

        &.contact {
          background-color: #fff;
          border: 1rpx solid #16a085;
          color: #16a085;

          &:active {
            background: linear-gradient(135deg, #f0fffe, #e8f8f5);
          }
        }

        &.reject {
          background-color: #fff;
          border: 1rpx solid #ff4d4f;
          color: #ff4d4f;

          &:active {
            background-color: #fff1f0;
          }
        }

        &.accept {
          background: linear-gradient(135deg, #0d7377, #14a085);
          color: #fff;

          &:active {
            background: linear-gradient(135deg, #0a5d61, #117a65);
          }
        }

        &.logistics {
          background-color: #fff;
          border: 1rpx solid #16a085;
          color: #16a085;

          &:active {
            background: linear-gradient(135deg, #f0fffe, #e8f8f5);
          }
        }

        .action-text {
          letter-spacing: 1rpx;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 加载更多
.load-more {
  padding: 30rpx;
  text-align: center;

  .loading-text, .no-more-text, .pull-up-text {
    font-size: 24rpx;
    color: #999;
  }

  .loading-text {
    color: #16a085;
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .status-tabs {
    padding: 0 12rpx; // 小屏幕进一步减少左边距

    .tab-item {
      padding: 20rpx 16rpx;
      min-width: 80rpx;

      .tab-text {
        font-size: 26rpx;
      }
    }
  }

  .order-list {
    padding: 16rpx 20rpx;

    .order-item {
      .order-content {
        padding: 24rpx 20rpx;

        .order-header {
          .order-header-left {
            gap: 10rpx;
          }
        }
      }

      .product-info {
        .product-image {
          width: 100rpx;
          height: 100rpx;
        }

        .product-details {
          .product-name {
            font-size: 24rpx;
          }

          .product-code {
            font-size: 20rpx;
          }
        }

        .price-info {
          .price-amount {
            .price-value {
              font-size: 28rpx;
            }
          }
        }
      }

      .order-actions {
        .action-btn {
          padding: 10rpx 20rpx;
          font-size: 22rpx;
        }
      }
    }
  }
}

// 加载更多样式
.load-more {
  padding: 30rpx;
  text-align: center;

  .loading-text, .no-more-text, .pull-up-text {
    font-size: 24rpx;
    color: #999;
  }

  .loading-text {
    color: #1989fa;
  }
}

// 订单列表滚动区域
.order-list {
  flex: 1;
  height: calc(100vh - 120rpx); // 减去状态标签页的高度
  width: 100%;
  box-sizing: border-box;
}

// 小屏幕适配
@media (max-width: 750rpx) {
  .order-list {
    padding: 16rpx 12rpx; // 小屏幕上减少内边距
  }

  .order-item {
    .order-content {
      padding: 24rpx 20rpx; // 减少订单项内边距
    }

    .product-info {
      .product-image {
        width: 100rpx;
        height: 100rpx;

        &.batch-icon-container {
          // 小屏幕上调整图标大小
          u-icon {
            font-size: 32rpx;
          }
        }
      }
    }
  }
}
</style>