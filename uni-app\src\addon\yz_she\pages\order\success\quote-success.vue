<template>
  <view class="quote-success-page">
    <!-- 商品信息卡片 -->
    <view class="product-card">
      <view class="product-info">
        <image
          :src="productInfo.image || '/static/images/default-product.png'"
          class="product-image"
          mode="aspectFit"
          @error="onImageError"
        ></image>
        <view class="product-details">
          <text class="product-name">{{ productInfo.name }}</text>
          <text class="product-code" v-if="productInfo.code">{{ productInfo.code }}</text>
        </view>
        <view class="quote-icon">
          <u-icon name="edit-pen-fill" color="#16a085" size="32"></u-icon>
        </view>
      </view>

      <view class="price-section">
        <view class="price-info">
          <text class="price-label">预估价格</text>
          <view class="price-amount">
            <text class="currency">¥</text>
            <text class="price-value">{{ productInfo.price }}</text>
          </view>
        </view>

        <view class="quote-note">
          <text class="note-text">*最终价格以实物质检为准</text>
        </view>
      </view>
    </view>

    <!-- 成功状态区域 -->
    <view class="success-section">
      <view class="success-icon">
        <view class="quote-bg">
          <view class="quote-body">
            <view class="quote-lines">
              <view class="line"></view>
              <view class="line"></view>
              <view class="line"></view>
            </view>
          </view>
          <view class="quote-clip"></view>
        </view>
        <view class="check-mark">
          <u-icon name="checkmark" color="#fff" size="16"></u-icon>
        </view>
      </view>

      <text class="success-title">估价提交成功</text>
      <text class="order-number">估价单号: {{ quoteInfo.orderNo }}</text>
      <text class="success-desc">我们将在24小时内为您提供专业估价</text>

      <!-- 查看估价单按钮 -->
      <view class="view-quote-button" @click="viewQuote">
        <text class="button-text">查看估价单</text>
      </view>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getQuoteOrderDetail } from '@/addon/yz_she/api/quote'
import { img } from '@/utils/common'

// 页面参数
const pageParams = ref<any>({})

// 商品信息
const productInfo = ref({
  name: '',
  code: '',
  image: '',
  price: '0.00'
})

// 估价单信息
const quoteInfo = ref({
  orderNo: '',
  status: '',
  createTime: ''
})

// 估价订单信息
const quoteOrderInfo = ref<any>({})

// 页面加载时获取参数
onLoad((options) => {
  console.log('页面参数:', options)
  pageParams.value = options

  if (options.id) {
    loadQuoteDetails(options.id)
  } else {
    // 如果没有传递ID，使用默认数据
    setDefaultData()
  }
})

// 加载估价详情
const loadQuoteDetails = async (quoteId: string) => {
  try {
    const response = await getQuoteOrderDetail(parseInt(quoteId))
    if (response.code === 1) {
      const data = response.data
      quoteOrderInfo.value = data

      // 设置商品信息
      productInfo.value = {
        name: data.product_name || data.brand_name || '估价商品',
        code: data.product_code || '',
        image: data.product_image ? img(data.product_image) : '',
        price: data.quote_price?.toFixed(2) || '0.00'
      }

      // 设置估价单信息
      quoteInfo.value = {
        orderNo: data.order_no || '',
        status: data.status_text || '',
        createTime: data.create_time_text || ''
      }
    } else {
      throw new Error(response.msg || '获取估价详情失败')
    }
  } catch (error) {
    console.error('加载估价详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    setDefaultData()
  }
}

// 设置默认数据
const setDefaultData = () => {
  productInfo.value = {
    name: '估价商品',
    code: '',
    image: '',
    price: '0.00'
  }

  quoteInfo.value = {
    orderNo: 'QT' + Date.now(),
    status: '待估价',
    createTime: new Date().toLocaleString()
  }
}

// 图片加载错误处理
const onImageError = () => {
  productInfo.value.image = '/static/images/default-product.png'
}

// 查看估价单
const viewQuote = () => {
  if (quoteOrderInfo.value.id) {
    uni.navigateTo({
      url: `/addon/yz_she/pages/order/detail/quote-detail?id=${quoteOrderInfo.value.id}`
    })
  } else {
    uni.navigateTo({
      url: '/addon/yz_she/pages/order/quote-list'
    })
  }
}
</script>

<style lang="scss" scoped>
.quote-success-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8fffe 0%, #f0f9f8 50%, #e8f5f3 100%);
  padding-top: 20rpx;
}

// 商品信息卡片
.product-card {
  background-color: #fff;
  margin: 24rpx 32rpx 20rpx 32rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.06);

  .product-info {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid rgba(22, 160, 133, 0.1);

    .product-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 12rpx;
      background-color: #f0f0f0;
    }

    .product-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8rpx;

      .product-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .product-code {
        font-size: 24rpx;
        color: #999;
      }
    }

    .quote-icon {
      width: 64rpx;
      height: 64rpx;
      background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2rpx solid rgba(22, 160, 133, 0.2);
      box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.1);
    }
  }

  .price-section {
    .price-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;

      .price-label {
        font-size: 26rpx;
        color: #666;
        font-weight: 500;
      }

      .price-amount {
        display: flex;
        align-items: baseline;
        gap: 4rpx;

        .currency {
          font-size: 24rpx;
          color: #16a085;
          font-weight: 600;
        }

        .price-value {
          font-size: 36rpx;
          color: #16a085;
          font-weight: 700;
        }
      }
    }

    .quote-note {
      .note-text {
        font-size: 22rpx;
        color: #999;
        font-style: italic;
      }
    }
  }
}

// 成功状态区域
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 32rpx 60rpx 32rpx;
  text-align: center;

  .success-icon {
    position: relative;
    margin-bottom: 32rpx;

    .quote-bg {
      width: 120rpx;
      height: 140rpx;
      background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
      border-radius: 12rpx 12rpx 8rpx 8rpx;
      position: relative;
      box-shadow: 0 8rpx 24rpx rgba(22, 160, 133, 0.3);

      .quote-body {
        padding: 20rpx 16rpx;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .quote-lines {
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .line {
            height: 4rpx;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 2rpx;

            &:nth-child(1) {
              width: 80%;
            }

            &:nth-child(2) {
              width: 60%;
            }

            &:nth-child(3) {
              width: 90%;
            }
          }
        }
      }

      .quote-clip {
        position: absolute;
        top: -8rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 16rpx;
        background: linear-gradient(135deg, #0d7377 0%, #14a085 100%);
        border-radius: 4rpx 4rpx 0 0;
        box-shadow: 0 2rpx 8rpx rgba(13, 115, 119, 0.4);
      }
    }

    .check-mark {
      position: absolute;
      bottom: -12rpx;
      right: -8rpx;
      width: 32rpx;
      height: 32rpx;
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.4);
      border: 3rpx solid #fff;
    }
  }

  .success-title {
    font-size: 36rpx;
    color: #333;
    font-weight: 700;
    margin-bottom: 16rpx;
    letter-spacing: 1rpx;
  }

  .order-number {
    font-size: 26rpx;
    color: #16a085;
    font-weight: 600;
    margin-bottom: 12rpx;
    font-family: 'Courier New', monospace;
  }

  .success-desc {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 40rpx;
    max-width: 400rpx;
  }

  // 查看估价单按钮
  .view-quote-button {
    padding: 12rpx 32rpx;
    background: linear-gradient(135deg, #fff 0%, #f8fffe 100%);
    border: 2rpx solid #16a085;
    border-radius: 32rpx;
    color: #16a085;
    font-size: 26rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2rpx 8rpx rgba(22, 160, 133, 0.15);

    &:active {
      transform: scale(0.98);
      background: linear-gradient(135deg, #f0fffe 0%, #e8f8f5 100%);
    }

    .button-text {
      letter-spacing: 1rpx;
    }
  }
}
</style>