/*
 Navicat Premium Dump SQL

 Source Server         : other_likeapi_cn
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26)
 Source Host           : localhost:3306
 Source Schema         : other_likeapi_cn

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26)
 File Encoding         : 65001

 Date: 31/07/2025 13:19:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for www_account_log
-- ----------------------------
DROP TABLE IF EXISTS `www_account_log`;
CREATE TABLE `www_account_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pay' COMMENT '账单类型pay,refund,transfer',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '对应类型交易单号',
  `create_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '站点账单记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_addon
-- ----------------------------
DROP TABLE IF EXISTS `www_addon`;
CREATE TABLE `www_addon`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件图标',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '插件描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态',
  `author` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '作者',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `install_time` int(11) NOT NULL DEFAULT 0 COMMENT '安装时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '封面',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'app' COMMENT '插件类型app，addon',
  `support_app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件支持的应用空表示通用插件',
  `is_star` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否加星',
  `compile` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '编译端口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '插件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_addon_log
-- ----------------------------
DROP TABLE IF EXISTS `www_addon_log`;
CREATE TABLE `www_addon_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `action` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作类型   install 安装 uninstall 卸载 update 更新',
  `key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `from_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级前的版本号',
  `to_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级后的版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '插件日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_applet_site_version
-- ----------------------------
DROP TABLE IF EXISTS `www_applet_site_version`;
CREATE TABLE `www_applet_site_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `version_id` int(11) NOT NULL DEFAULT 0 COMMENT '版本id',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序类型',
  `action` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作方式 download 下载  upgrade 更新',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '站点小程序版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_applet_version
-- ----------------------------
DROP TABLE IF EXISTS `www_applet_version`;
CREATE TABLE `www_applet_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置信息',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序类型',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '插件描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态  下架  上架',
  `uid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布者',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序包地址',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `version_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号数字(用于排序)',
  `release_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布线上版本号',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_form
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form`;
CREATE TABLE `www_diy_form`  (
  `form_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表单id',
  `page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称（用于后台展示）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称（用于前台展示）',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单类型',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0，关闭，1：开启）',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单数据，json格式，包含展示组件',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件标识',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `write_num` int(11) NOT NULL DEFAULT 0 COMMENT '表单填写总数量',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注说明',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`form_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_fields
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_fields`;
CREATE TABLE `www_diy_form_fields`  (
  `field_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '字段id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `field_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段唯一标识',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段说明',
  `field_default` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字段默认值',
  `write_num` int(11) NOT NULL DEFAULT 0 COMMENT '字段填写总数量',
  `field_required` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否必填 0:否 1:是',
  `field_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否隐藏 0:否 1:是',
  `field_unique` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段内容防重复 0:否 1:是',
  `privacy_protection` tinyint(4) NOT NULL DEFAULT 0 COMMENT '隐私保护 0:关闭 1:开启',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`field_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_records
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_records`;
CREATE TABLE `www_diy_form_records`  (
  `record_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '表单填写记录id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '填写的表单数据',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '填写人会员id',
  `relate_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联业务id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_records_fields
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_records_fields`;
CREATE TABLE `www_diy_form_records_fields`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `form_field_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联表单字段id',
  `record_id` int(11) NOT NULL DEFAULT 0 COMMENT '关联表单填写记录id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '填写会员id',
  `field_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段唯一标识',
  `field_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段值，根据类型展示对应效果',
  `field_required` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否必填 0:否 1:是',
  `field_hidden` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段是否隐藏 0:否 1:是',
  `field_unique` tinyint(4) NOT NULL DEFAULT 0 COMMENT '字段内容防重复 0:否 1:是',
  `privacy_protection` tinyint(4) NOT NULL DEFAULT 0 COMMENT '隐私保护 0:关闭 1:开启',
  `update_num` int(11) NOT NULL DEFAULT 0 COMMENT '字段修改次数',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_submit_config
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_submit_config`;
CREATE TABLE `www_diy_form_submit_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `submit_after_action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '填表人提交后操作，text：文字信息，voucher：核销凭证',
  `tips_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提示内容类型，default：默认提示，diy：自定义提示',
  `tips_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '自定义提示内容',
  `time_limit_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '核销凭证有效期限制类型，no_limit：不限制，specify_time：指定固定开始结束时间，submission_time：按提交时间设置有效期',
  `time_limit_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '核销凭证时间限制规则，json格式',
  `voucher_content_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '核销凭证内容，json格式',
  `success_after_action` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '填写成功后续操作',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单提交页配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_form_write_config
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_form_write_config`;
CREATE TABLE `www_diy_form_write_config`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `form_id` int(11) NOT NULL DEFAULT 0 COMMENT '所属万能表单id',
  `write_way` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '填写方式，no_limit：不限制，scan：仅限微信扫一扫，url：仅限链接进入',
  `join_member_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all_member' COMMENT '参与会员，all_member：所有会员参与，selected_member_level：指定会员等级，selected_member_label：指定会员标签',
  `level_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员等级id集合',
  `label_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '会员标签id集合',
  `member_write_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '每人可填写次数，no_limit：不限制，diy：自定义',
  `member_write_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '每人可填写次数自定义规则',
  `form_write_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单可填写数量，no_limit：不限制，diy：自定义',
  `form_write_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单可填写总数自定义规则',
  `time_limit_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '填写时间限制类型，no_limit：不限制， specify_time：指定开始结束时间，open_day_time：设置每日开启时间',
  `time_limit_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '填写时间限制规则',
  `is_allow_update_content` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否允许修改自己填写的内容，0：否，1：是',
  `write_instruction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表单填写须知',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '万能表单填写配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_diy_page
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_page`;
CREATE TABLE `www_diy_page`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面名称（用于后台展示）',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标题（用于前台展示）',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标识',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面模板',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'diy' COMMENT '页面展示模式，diy：自定义，fixed：固定',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '页面数据，json格式',
  `is_default` int(11) NOT NULL DEFAULT 0 COMMENT '是否默认页面，1：是，0：否',
  `is_change` int(11) NOT NULL DEFAULT 0 COMMENT '数据是否发生过变化，1：变化了，2：没有',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `visit_count` int(11) NOT NULL DEFAULT 0 COMMENT '访问量',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义页面' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_route
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_route`;
CREATE TABLE `www_diy_route`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面名称',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面标识',
  `page` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '页面路径',
  `share` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分享内容',
  `is_share` int(11) NOT NULL DEFAULT 0 COMMENT '是否支持分享',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义路由' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_diy_theme
-- ----------------------------
DROP TABLE IF EXISTS `www_diy_theme`;
CREATE TABLE `www_diy_theme`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件类型app，addon',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属应用，app：系统，shop：商城、o2o：上门服务',
  `mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模式，default：默认【跟随系统】，diy：自定义配色',
  `theme_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配色类型，default：默认，diy：自定义',
  `default_theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前色调的默认值',
  `theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '当前色调',
  `new_theme` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '新增颜色集合',
  `is_selected` tinyint(4) NOT NULL DEFAULT 0 COMMENT '已选色调，0：否，1.是',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '自定义主题配色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_generate_column
-- ----------------------------
DROP TABLE IF EXISTS `www_generate_column`;
CREATE TABLE `www_generate_column`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `table_id` int(11) NOT NULL DEFAULT 0 COMMENT '表id',
  `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段名称',
  `column_comment` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字段类型',
  `is_required` tinyint(1) NULL DEFAULT 0 COMMENT '是否必填 0-非必填 1-必填',
  `is_pk` tinyint(1) NULL DEFAULT 0 COMMENT '是否为主键 0-不是 1-是',
  `is_insert` tinyint(1) NULL DEFAULT 0 COMMENT '是否为插入字段 0-不是 1-是',
  `is_update` tinyint(1) NULL DEFAULT 0 COMMENT '是否为更新字段 0-不是 1-是',
  `is_lists` tinyint(1) NULL DEFAULT 1 COMMENT '是否为列表字段 0-不是 1-是',
  `is_query` tinyint(1) NULL DEFAULT 1 COMMENT '是否为查询字段 0-不是 1-是',
  `is_search` tinyint(1) NULL DEFAULT 1 COMMENT '是否搜索字段',
  `query_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '=' COMMENT '查询类型',
  `view_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'input' COMMENT '显示类型',
  `dict_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉关联应用',
  `model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉关联model',
  `label_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉标题字段',
  `value_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '远程下拉value字段',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `is_delete` tinyint(4) NULL DEFAULT 0 COMMENT '是否为软删除字段 0-不是 1-是',
  `is_order` tinyint(4) NULL DEFAULT 0 COMMENT '是否为排序字段 0-不是 1-是',
  `validate_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表字段信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_generate_table
-- ----------------------------
DROP TABLE IF EXISTS `www_generate_table`;
CREATE TABLE `www_generate_table`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表名',
  `table_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述前缀',
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块名',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类名前缀',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `edit_type` int(11) NOT NULL DEFAULT 1 COMMENT '编辑方式 1-弹框 2-新页面',
  `addon_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件名',
  `order_type` int(11) NOT NULL DEFAULT 0 COMMENT '排序方式 0-无排序 1-正序 2-倒序',
  `parent_menu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上级菜单',
  `relations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联配置',
  `synchronous_number` int(11) NOT NULL DEFAULT 0 COMMENT '同步次数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_jobs
-- ----------------------------
DROP TABLE IF EXISTS `www_jobs`;
CREATE TABLE `www_jobs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `attempts` tinyint(4) UNSIGNED NOT NULL DEFAULT 0,
  `reserve_time` int(11) UNSIGNED NULL DEFAULT 0,
  `available_time` int(11) UNSIGNED NULL DEFAULT 0,
  `create_time` int(11) UNSIGNED NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息队列任务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_jobs_failed
-- ----------------------------
DROP TABLE IF EXISTS `www_jobs_failed`;
CREATE TABLE `www_jobs_failed`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fail_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息队列任务失败记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member
-- ----------------------------
DROP TABLE IF EXISTS `www_member`;
CREATE TABLE `www_member`  (
  `member_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `member_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员编码',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '推广会员id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员用户名',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员密码',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员昵称',
  `headimg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员头像',
  `member_level` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `member_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会员标签',
  `wx_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信用户openid',
  `weapp_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信小程序openid',
  `wx_unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信unionid',
  `ali_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付宝账户id',
  `douyin_openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '抖音小程序openid',
  `register_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'H5' COMMENT '注册来源',
  `register_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册方式',
  `login_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前登录ip',
  `login_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'h5' COMMENT '当前登录的操作终端类型',
  `login_channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `login_count` int(11) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `login_time` int(11) NOT NULL DEFAULT 0 COMMENT '当前登录时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '注册时间',
  `last_visit_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后访问时间',
  `last_consum_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后消费时间',
  `sex` tinyint(4) NOT NULL DEFAULT 0 COMMENT '性别 0保密 1男 2女',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '用户状态  用户状态默认为1',
  `birthday` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '出生日期',
  `id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `point` int(11) NOT NULL DEFAULT 0 COMMENT '可用积分',
  `point_get` int(11) NOT NULL DEFAULT 0 COMMENT '累计获取积分',
  `balance` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额',
  `balance_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计获取余额',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '可用余额（可提现）',
  `money_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计获取余额（可提现）',
  `money_cash_outing` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现中余额（可提现）',
  `growth` int(11) NOT NULL DEFAULT 0 COMMENT '成长值',
  `growth_get` int(11) NOT NULL DEFAULT 0 COMMENT '累计获得成长值',
  `commission` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '当前佣金',
  `commission_get` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金获取',
  `commission_cash_outing` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现中佣金',
  `is_member` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是会员',
  `member_time` int(11) NOT NULL DEFAULT 0 COMMENT '成为会员时间',
  `is_del` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0正常  1已删除',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '定位地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_account_log
-- ----------------------------
DROP TABLE IF EXISTS `www_member_account_log`;
CREATE TABLE `www_member_account_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'point' COMMENT '账户类型',
  `account_data` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '账户数据',
  `account_sum` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变动后的账户余额',
  `from_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源类型',
  `related_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联Id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_address
-- ----------------------------
DROP TABLE IF EXISTS `www_member_address`;
CREATE TABLE `www_member_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户姓名',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省id',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '市id',
  `district_id` int(11) NOT NULL DEFAULT 0 COMMENT '区县id',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '地址信息',
  `address_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `full_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '详细地址信息',
  `lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `lat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `is_default` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是默认地址',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IDX_member_address`(`member_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员收货地址' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_cash_out
-- ----------------------------
DROP TABLE IF EXISTS `www_member_cash_out`;
CREATE TABLE `www_member_cash_out`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cash_out_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现交易号',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'money' COMMENT '提现账户类型',
  `transfer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '转账提现类型',
  `transfer_realname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `transfer_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `transfer_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `transfer_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款账号',
  `transfer_payee` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账收款方(json),主要用于对接在线的打款方式',
  `transfer_payment_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码图片',
  `transfer_fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `transfer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账状态',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT '转账时间',
  `apply_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现申请金额',
  `rate` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费比率',
  `service_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现手续费',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现到账金额',
  `audit_time` int(11) NOT NULL DEFAULT 0 COMMENT '审核时间',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '状态1待审核2.待转账3已转账 -1拒绝 -2 已取消',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `refuse_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `transfer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账单号',
  `cancel_time` int(11) NOT NULL DEFAULT 0 COMMENT '取消时间',
  `final_transfer_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账方式',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_cash_out_account
-- ----------------------------
DROP TABLE IF EXISTS `www_member_cash_out_account`;
CREATE TABLE `www_member_cash_out_account`  (
  `account_id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `account_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '账户类型',
  `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `realname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '真实名称',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `account_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现账户',
  `transfer_payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码',
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员提现账户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_label
-- ----------------------------
DROP TABLE IF EXISTS `www_member_label`;
CREATE TABLE `www_member_label`  (
  `label_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `label_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签名称',
  `memo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`label_id`) USING BTREE,
  INDEX `label_id`(`label_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员标签' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_level
-- ----------------------------
DROP TABLE IF EXISTS `www_member_level`;
CREATE TABLE `www_member_level`  (
  `level_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '会员等级',
  `level_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '等级名称',
  `growth` int(11) NOT NULL DEFAULT 0 COMMENT '所需成长值',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态 0已禁用1已启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `level_benefits` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级权益',
  `level_gifts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '等级礼包',
  PRIMARY KEY (`level_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员等级' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_member_sign
-- ----------------------------
DROP TABLE IF EXISTS `www_member_sign`;
CREATE TABLE `www_member_sign`  (
  `sign_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `days` int(11) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `day_award` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '日签奖励',
  `continue_award` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '连签奖励',
  `continue_tag` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '连签奖励标识',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '签到时间',
  `start_time` int(11) NOT NULL DEFAULT 0 COMMENT '签到周期开始时间',
  `is_sign` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否签到（0未签到 1已签到）',
  PRIMARY KEY (`sign_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员签到表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_niu_sms_template
-- ----------------------------
DROP TABLE IF EXISTS `www_niu_sms_template`;
CREATE TABLE `www_niu_sms_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sms_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信服务商类型 niuyun-牛云 aliyun-阿里云 tencent-腾讯',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '子账号名称',
  `template_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版key',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版id',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版类型',
  `template_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版内容',
  `param_json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '参数变量',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上下架状态',
  `audit_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '报备、审核状态',
  `audit_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核结果/拒绝原因',
  `report_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报备、审核信息',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '牛云短信模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_pay
-- ----------------------------
DROP TABLE IF EXISTS `www_pay`;
CREATE TABLE `www_pay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '支付会员id',
  `from_main_id` int(11) NOT NULL DEFAULT 0 COMMENT '发起支付会员id',
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水号',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `trade_id` int(11) NOT NULL DEFAULT 0 COMMENT '业务id',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `body` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付主体',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付票据',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '支付状态（0.待支付 1. 支付中 2. 已支付 -1已取消）',
  `json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付扩展用支付信息',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `pay_time` int(11) NOT NULL DEFAULT 0 COMMENT '支付时间',
  `cancel_time` int(11) NOT NULL DEFAULT 0 COMMENT '关闭时间',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `mch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户收款账号',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_channel
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_channel`;
CREATE TABLE `www_pay_channel`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付类型',
  `channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付配置',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `status` int(11) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付渠道配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_refund
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_refund`;
CREATE TABLE `www_pay_refund`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款单号',
  `out_trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付流水号',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付方式',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付渠道',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款原因',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '支付状态（0.待退款 1. 退款中 2. 已退款 -1已关闭）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `refund_time` int(11) NOT NULL DEFAULT 0 COMMENT '支付时间',
  `close_time` int(11) NOT NULL DEFAULT 0 COMMENT '关闭时间',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付凭证',
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `trade_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务关联id',
  `refund_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '退款方式',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作人类型',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人',
  `pay_refund_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部支付方式的退款单号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付退款记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_transfer
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_transfer`;
CREATE TABLE `www_pay_transfer`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trade_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `transfer_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账单号',
  `main_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `main_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主体类型',
  `transfer_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账类型',
  `transfer_realname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系人名称',
  `transfer_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `transfer_bank` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '银行名称',
  `transfer_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款账号',
  `transfer_voucher` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '凭证',
  `transfer_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '凭证说明',
  `transfer_payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '收款码图片',
  `transfer_fail_reason` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `transfer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账状态',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '转账金额',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '申请时间',
  `transfer_time` int(11) NOT NULL DEFAULT 0 COMMENT '转账时间',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `openid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `batch_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账批次id',
  `transfer_payee` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '在线转账数据(json)',
  `out_batch_no` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展数据,主要用于记录接收到线上打款的业务数据编号',
  `package_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转领取页面的package信息',
  `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '转账表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_pay_transfer_scene
-- ----------------------------
DROP TABLE IF EXISTS `www_pay_transfer_scene`;
CREATE TABLE `www_pay_transfer_scene`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务类型',
  `scene` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '场景',
  `infos` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账报备背景',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `perception` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '转账收款感知',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付转账场景表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for www_sys_agreement
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_agreement`;
CREATE TABLE `www_sys_agreement`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `agreement_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议关键字',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '协议标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '协议内容',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '协议表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_area
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_area`;
CREATE TABLE `www_sys_area`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `shortname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '简称',
  `longitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `latitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `level` smallint(6) NOT NULL DEFAULT 0 COMMENT '级别',
  `sort` mediumint(9) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态1有效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 460400501 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_attachment
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_attachment`;
CREATE TABLE `www_sys_attachment`  (
  `att_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始文件名',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '完整地址',
  `dir` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件路径',
  `att_size` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件大小',
  `att_type` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '附件类型image,video',
  `storage_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片上传类型 local本地  aliyun  阿里云oss  qiniu  七牛 ....',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '相关分类',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '上传时间',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '网络地址',
  PRIMARY KEY (`att_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_attachment_category
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_attachment_category`;
CREATE TABLE `www_sys_attachment_category`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '父级ID',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件管理类型（image,video）',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `enname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类目录',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_backup_records
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_backup_records`;
CREATE TABLE `www_sys_backup_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份版本号',
  `backup_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份标识',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备份内容',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态',
  `fail_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '失败原因',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `complete_time` int(11) NOT NULL DEFAULT 0 COMMENT '完成时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '备份记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_config
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_config`;
CREATE TABLE `www_sys_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置项关键字',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值json',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用 1启用 0不启用',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_cron_task
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_cron_task`;
CREATE TABLE `www_sys_cron_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '任务状态',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '执行次数',
  `title` char(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务模式  cron  定时任务  crond 周期任务',
  `crond_type` char(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务周期',
  `crond_length` int(11) NOT NULL DEFAULT 0 COMMENT '任务周期',
  `task` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务命令',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附加参数',
  `status_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上次执行结果',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后执行时间',
  `next_time` int(11) NOT NULL DEFAULT 0 COMMENT '下次执行时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = ' 系统任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_dict`;
CREATE TABLE `www_sys_dict`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典关键词',
  `dictionary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典数据',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据字典表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_export
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_export`;
CREATE TABLE `www_sys_export`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `export_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '主题关键字',
  `export_num` int(11) NOT NULL DEFAULT 0 COMMENT '导出数据数量',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件存储路径',
  `file_size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件大小',
  `export_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '导出状态',
  `fail_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '导出时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '导出报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_menu`;
CREATE TABLE `www_sys_menu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `app_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'admin' COMMENT '应用类型',
  `menu_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
  `menu_short_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单短标题',
  `menu_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单标识（菜单输入，接口自动生成）',
  `parent_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '父级key',
  `menu_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '菜单类型 0目录 1菜单 2按钮',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标 菜单有效',
  `api_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api接口地址',
  `router_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单路由地址 前端使用',
  `view_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单文件地址',
  `methods` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提交方式POST GET PUT DELETE',
  `sort` int(11) NOT NULL DEFAULT 1 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '正常，禁用（禁用后不允许访问）',
  `is_show` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否显示',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `delete_time` int(11) NOT NULL DEFAULT 0,
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'system' COMMENT '菜单来源   system 系统文件  create 新建菜单  generator 代码生成器',
  `menu_attr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单属性 common 公共 system 系统',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26081 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice`;
CREATE TABLE `www_sys_notice`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标识',
  `sms_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信配置参数',
  `is_wechat` tinyint(4) NOT NULL DEFAULT 0 COMMENT '公众号模板消息（0：关闭，1：开启）',
  `is_weapp` tinyint(4) NOT NULL DEFAULT 0 COMMENT '小程序订阅消息（0：关闭，1：开启）',
  `is_sms` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发送短信（0：关闭，1：开启）',
  `wechat_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信模版消息id',
  `weapp_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信小程序订阅消息id',
  `sms_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '短信id（对应短信配置）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `wechat_first` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信头部',
  `wechat_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知模型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice_log`;
CREATE TABLE `www_sys_notice_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '通知记录ID',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '消息key',
  `notice_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'sms' COMMENT '消息类型（sms,wechat.weapp）',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '通知的用户id',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '消息的会员id',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接收人用户昵称或姓名',
  `receiver` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '接收人（对应手机号，openid）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息数据',
  `is_click` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `is_visit` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '访问次数',
  `visit_time` int(11) NOT NULL DEFAULT 0 COMMENT '访问时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消息时间',
  `result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '结果',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_notice_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_notice_sms_log`;
CREATE TABLE `www_sys_notice_sms_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `sms_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送关键字（注册、找回密码）',
  `key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送关键字（注册、找回密码）',
  `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送内容',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据参数',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'sending' COMMENT '发送状态：sending-发送中；success-发送成功；fail-发送失败',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '短信结果',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `send_time` int(11) NOT NULL DEFAULT 0 COMMENT '发送时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_poster
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_poster`;
CREATE TABLE `www_sys_poster`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报类型',
  `channel` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '海报支持渠道',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置值json',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用 1启用 2不启用',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `is_default` int(11) NOT NULL DEFAULT 0 COMMENT '是否默认海报，1：是，0：否',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '海报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_printer
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_printer`;
CREATE TABLE `www_sys_printer`  (
  `printer_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `printer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机名称',
  `brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '设备品牌（易联云，365，飞鹅）',
  `printer_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机编号',
  `printer_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打印机秘钥',
  `open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开发者id',
  `apikey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开发者密钥',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小票打印模板类型，多个逗号隔开',
  `trigger` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '触发打印时机',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '打印模板数据，json格式',
  `print_width` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '58mm' COMMENT '纸张宽度',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态（0，关闭，1：开启）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`printer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小票打印机' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_printer_template
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_printer_template`;
CREATE TABLE `www_sys_printer_template`  (
  `template_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `template_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板类型',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板数据，json格式',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小票打印模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_role
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_role`;
CREATE TABLE `www_sys_role`  (
  `role_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色权限(menus_id)',
  `addon_keys` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色应用权限（应用key）',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后修改时间',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_schedule
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_schedule`;
CREATE TABLE `www_sys_schedule`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务模板key',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '任务状态 是否启用',
  `time` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务周期  json结构',
  `count` int(11) NOT NULL DEFAULT 0 COMMENT '执行次数',
  `last_time` int(11) NOT NULL DEFAULT 0 COMMENT '最后执行时间',
  `next_time` int(11) NOT NULL DEFAULT 0 COMMENT '下次执行时间',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_schedule_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_schedule_log`;
CREATE TABLE `www_sys_schedule_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '执行记录id',
  `schedule_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务id',
  `addon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '所属插件',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务模板key',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '计划任务名称',
  `execute_time` int(11) NOT NULL COMMENT '执行时间',
  `execute_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '日志信息',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '执行状态',
  `class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `job` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '计划任务执行记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_upgrade_records
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_upgrade_records`;
CREATE TABLE `www_sys_upgrade_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `upgrade_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '升级标识',
  `app_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件标识',
  `name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '升级名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '升级内容',
  `prev_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '前一版本',
  `current_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '当前版本',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '状态',
  `fail_reason` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '失败原因',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `complete_time` int(11) NOT NULL DEFAULT 0 COMMENT '完成时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '升级记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user`;
CREATE TABLE `www_sys_user`  (
  `uid` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '系统用户ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户账号',
  `head_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户密码',
  `real_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '实际姓名',
  `last_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '最后一次登录ip',
  `last_time` int(10) NOT NULL DEFAULT 0 COMMENT '最后一次登录时间',
  `create_time` int(10) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `login_count` int(10) NOT NULL DEFAULT 0 COMMENT '登录次数',
  `is_del` tinyint(3) NOT NULL DEFAULT 0,
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` tinyint(3) NOT NULL DEFAULT 1 COMMENT '后台管理员状态 1有效0无效',
  `role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权限组',
  `is_admin` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否是管理员',
  PRIMARY KEY (`uid`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '后台管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user_log
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user_log`;
CREATE TABLE `www_sys_user_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员操作记录ID',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登录IP',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '管理员姓名',
  `url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `params` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求方式',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 907 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `www_sys_user_role`;
CREATE TABLE `www_sys_user_role`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `role_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '角色id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `is_admin` int(11) NOT NULL DEFAULT 0 COMMENT '是否是超级管理员',
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_verifier
-- ----------------------------
DROP TABLE IF EXISTS `www_verifier`;
CREATE TABLE `www_verifier`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '会员id',
  `uid` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '添加时间',
  `verify_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_verify
-- ----------------------------
DROP TABLE IF EXISTS `www_verify`;
CREATE TABLE `www_verify`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `data` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销参数',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销类型',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '核销时间',
  `verifier_member_id` int(11) NOT NULL DEFAULT 0 COMMENT '核销会员id',
  `value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '核销内容',
  `body` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `relate_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核销记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_weapp_version
-- ----------------------------
DROP TABLE IF EXISTS `www_weapp_version`;
CREATE TABLE `www_weapp_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `version_no` int(11) NOT NULL DEFAULT 1,
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '说明',
  `create_time` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `update_time` int(11) NOT NULL DEFAULT 0,
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `task_key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上传任务key',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序版本' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_web_adv
-- ----------------------------
DROP TABLE IF EXISTS `www_web_adv`;
CREATE TABLE `www_web_adv`  (
  `adv_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `adv_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '广告位key',
  `adv_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告内容描述',
  `adv_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告链接',
  `adv_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告内容图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `background` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '#FFFFFF' COMMENT '背景色',
  PRIMARY KEY (`adv_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_web_friendly_link
-- ----------------------------
DROP TABLE IF EXISTS `www_web_friendly_link`;
CREATE TABLE `www_web_friendly_link`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '索引id',
  `link_title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `link_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接',
  `link_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序号',
  `is_show` int(11) NOT NULL DEFAULT 1 COMMENT '是否显示 1.是 2.否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电脑端友情链接表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_web_nav
-- ----------------------------
DROP TABLE IF EXISTS `www_web_nav`;
CREATE TABLE `www_web_nav`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nav_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '导航名称',
  `nav_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接地址',
  `sort` int(11) NOT NULL COMMENT '排序号',
  `is_blank` int(11) NULL DEFAULT 0 COMMENT '是否新打开',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '修改时间',
  `is_show` smallint(6) NOT NULL DEFAULT 1 COMMENT '是否显示 1显示 0不显示',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'PC导航管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_wechat_fans
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_fans`;
CREATE TABLE `www_wechat_fans`  (
  `fans_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '粉丝ID',
  `nickname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `sex` smallint(6) NOT NULL DEFAULT 1 COMMENT '性别',
  `language` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户语言',
  `country` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '国家',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '行政区/县',
  `openid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户的标识，对当前公众号唯一     用户的唯一身份ID',
  `unionid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '粉丝unionid',
  `groupid` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝所在组id',
  `is_subscribe` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否订阅',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `subscribe_time` int(11) NOT NULL DEFAULT 0 COMMENT '关注时间',
  `subscribe_scene` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '返回用户关注的渠道来源',
  `unsubscribe_time` int(11) NOT NULL DEFAULT 0 COMMENT '取消关注时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '粉丝信息最后更新时间',
  `app_id` int(11) NOT NULL DEFAULT 0 COMMENT '应用appid',
  PRIMARY KEY (`fans_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信粉丝列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_wechat_media
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_media`;
CREATE TABLE `www_wechat_media`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '值',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  `media_id` varchar(70) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '微信端返回的素材id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信素材表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `www_wechat_reply`;
CREATE TABLE `www_wechat_reply`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '规则名称',
  `keyword` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关键词',
  `reply_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复类型 subscribe-关注回复 keyword-关键字回复 default-默认回复',
  `matching_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '匹配方式：full 全匹配；like-模糊匹配',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复内容',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 50 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
  `reply_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '回复方式 all 全部 rand随机',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公众号消息回调表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for www_yz_she_brands
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_brands`;
CREATE TABLE `www_yz_she_brands`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '品牌名称',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌Logo',
  `letter` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'A' COMMENT '首字母索引',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门品牌 1是 0否',
  `hot_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热门品牌名字',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_letter`(`letter`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_categories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_categories`;
CREATE TABLE `www_yz_she_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图标',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类图片',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1启用 0禁用',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_category_accessories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_category_accessories`;
CREATE TABLE `www_yz_she_category_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `accessory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类配件配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_category_photos
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_category_photos`;
CREATE TABLE `www_yz_she_category_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `photo_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `background_image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'upload/background/default-photo-bg.png' COMMENT '背景图片',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类照片配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_express_log
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_express_log`;
CREATE TABLE `www_yz_she_express_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `waybill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单号',
  `shopbill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家单号',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运单状态描述',
  `type_code` tinyint(1) NULL DEFAULT NULL COMMENT '状态码：1待揽收，2运输中，3已签收，4拒收退回，99已取消',
  `weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '下单重量',
  `real_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '站点称重',
  `transfer_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '分拣称重',
  `cal_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '计费重量',
  `volume` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积',
  `parse_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '体积换算重量',
  `total_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '运单总扣款费用',
  `freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '快递费',
  `freight_insured` decimal(10, 2) NULL DEFAULT NULL COMMENT '保价费',
  `freight_haocai` decimal(10, 2) NULL DEFAULT NULL COMMENT '增值费用',
  `change_bill` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '换单号',
  `change_bill_freight` decimal(10, 2) NULL DEFAULT NULL COMMENT '逆向费',
  `fee_over` tinyint(1) NULL DEFAULT NULL COMMENT '订单扣费状态：1已扣费，0冻结',
  `courier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员姓名',
  `courier_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员电话',
  `pickup_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取件码',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调原始内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_waybill`(`waybill`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流回调日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yz_she_goods
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_goods`;
CREATE TABLE `www_yz_she_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `brand_id` int(11) NOT NULL DEFAULT 0 COMMENT '品牌ID',
  `category_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品货号/型号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片JSON格式',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品描述',
  `price_new` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全新品回收价格',
  `price_used` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '二手品回收价格',
  `price_damaged` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '有瑕疵回收价格',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门商品 1是 0否',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态 1上架 0下架',
  `create_time` int(11) NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_code`(`code`) USING BTREE,
  INDEX `idx_is_hot`(`is_hot`) USING BTREE,
  INDEX `idx_sort`(`sort`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_order_status_logs
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_order_status_logs`;
CREATE TABLE `www_yz_she_order_status_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `operator_id`(`operator_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  CONSTRAINT `fk_status_logs_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单状态变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_accessories
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_accessories`;
CREATE TABLE `www_yz_she_quote_accessories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配件ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `accessory_config_id` int(11) NULL DEFAULT NULL COMMENT '配件配置ID',
  `accessory_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配件名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `accessory_config_id`(`accessory_config_id`) USING BTREE,
  INDEX `idx_accessories_order`(`quote_order_id`) USING BTREE,
  CONSTRAINT `fk_quote_accessories_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单配件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_orders
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_orders`;
CREATE TABLE `www_yz_she_quote_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片/品牌图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=估价中,2=待确认,3=待发货,4=已完成,5=已取消',
  `quote_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '估价金额',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '估价管理员ID',
  `quote_time` datetime NULL DEFAULT NULL COMMENT '估价时间',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '用户确认时间',
  `ship_time` datetime NULL DEFAULT NULL COMMENT '用户发货时间',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
  `auto_cancel_time` datetime NULL DEFAULT NULL COMMENT '自动取消时间(确认后48小时)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `brand_id`(`brand_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `auto_cancel_time`(`auto_cancel_time`) USING BTREE,
  INDEX `idx_orders_user_status_time`(`user_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_orders_status_time`(`status`, `create_time`) USING BTREE,
  INDEX `idx_orders_auto_cancel`(`status`, `auto_cancel_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_photos
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_photos`;
CREATE TABLE `www_yz_she_quote_photos`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '照片ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `photo_config_id` int(11) NULL DEFAULT NULL COMMENT '照片配置ID',
  `photo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片类型',
  `photo_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片名称',
  `photo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '照片URL',
  `is_defect` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为瑕疵照片:0=否,1=是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `photo_config_id`(`photo_config_id`) USING BTREE,
  INDEX `photo_type`(`photo_type`) USING BTREE,
  INDEX `is_defect`(`is_defect`) USING BTREE,
  INDEX `idx_photos_order_type`(`quote_order_id`, `photo_type`, `sort`) USING BTREE,
  CONSTRAINT `fk_quote_photos_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价订单照片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_quote_records
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_quote_records`;
CREATE TABLE `www_yz_she_quote_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `quote_order_id` int(11) NOT NULL COMMENT '估价订单ID',
  `admin_id` int(11) NOT NULL COMMENT '估价管理员ID',
  `condition_score` int(3) NULL DEFAULT NULL COMMENT '成色评分(0-100)',
  `quote_price` decimal(10, 2) NOT NULL COMMENT '估价金额',
  `quote_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '估价说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `admin_id`(`admin_id`) USING BTREE,
  INDEX `create_time`(`create_time`) USING BTREE,
  INDEX `idx_records_order_time`(`quote_order_id`, `create_time`) USING BTREE,
  CONSTRAINT `fk_quote_records_order` FOREIGN KEY (`quote_order_id`) REFERENCES `www_yz_she_quote_orders` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '估价记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_recycle_order_logs
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_order_logs`;
CREATE TABLE `www_yz_she_recycle_order_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `recycle_order_id` int(11) NOT NULL COMMENT '回收订单ID',
  `from_status` tinyint(1) NULL DEFAULT NULL COMMENT '原状态',
  `to_status` tinyint(1) NOT NULL COMMENT '新状态',
  `operator_id` int(11) NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_type` tinyint(1) NOT NULL COMMENT '操作人类型:1=用户,2=管理员,3=系统',
  `change_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '备注',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '额外数据JSON',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_recycle_order_id`(`recycle_order_id`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单状态变更日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yz_she_recycle_orders
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_orders`;
CREATE TABLE `www_yz_she_recycle_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '回收订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '回收订单编号',
  `quote_order_id` int(11) NULL DEFAULT NULL COMMENT '关联的估价订单ID（从估价订单创建时有值）',
  `member_id` int(11) NOT NULL COMMENT '会员ID（对应member表）',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `brand_id` int(11) NULL DEFAULT NULL COMMENT '品牌ID',
  `product_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `product_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品编码',
  `product_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态:1=待取件,2=待收货,3=待质检,4=待确认,5=待退回,6=已退回,7=已完成',
  `source_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单来源:1=估价订单确认,2=直接回收,3=批量下单',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '回收数量',
  `expected_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '预期回收价格（用户选择的价格）',
  `final_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终回收价格（质检后确定）',
  `voucher_id` int(11) NULL DEFAULT NULL COMMENT '使用的加价券ID（对应www_yz_she_voucher表）',
  `voucher_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '加价券金额',
  `total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '最终结算金额',
  `delivery_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式:1=快递上门,2=自行寄出',
  `pickup_address_id` int(11) NULL DEFAULT NULL COMMENT '上门取件地址ID（对应www_member_address表）',
  `pickup_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望上门时间',
  `pickup_contact_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `pickup_contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `pickup_address_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细地址信息',
  `express_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递回调ID',
  `express_company` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单号',
  `express_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
  `quality_check_admin_id` int(11) NULL DEFAULT NULL COMMENT '质检员ID',
  `quality_score` int(3) NULL DEFAULT NULL COMMENT '质检评分(0-100)',
  `quality_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检说明',
  `quality_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质检照片JSON',
  `settlement_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '结算方式:1=余额结算',
  `settlement_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际结算金额',
  `settlement_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '结算状态:0=未结算,1=已结算',
  `settlement_admin_id` int(11) NULL DEFAULT NULL COMMENT '结算操作员ID',
  `user_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '用户备注',
  `admin_note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '管理员备注',
  `reject_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因（用户不接受质检价格）',
  `return_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退回原因',
  `pickup_time_actual` int(11) NULL DEFAULT NULL COMMENT '实际取件时间（状态1→2）',
  `receive_time` int(11) NULL DEFAULT NULL COMMENT '平台收货时间（状态2→3）',
  `quality_start_time` int(11) NULL DEFAULT NULL COMMENT '质检开始时间（状态3→4）',
  `quality_complete_time` int(11) NULL DEFAULT NULL COMMENT '质检完成时间（状态4完成）',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间（状态4→7或4→5）',
  `return_time` int(11) NULL DEFAULT NULL COMMENT '商品退回时间（状态5→6）',
  `settlement_time` int(11) NULL DEFAULT NULL COMMENT '结算完成时间（状态7）',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_no`(`order_no`) USING BTREE,
  INDEX `idx_quote_order_id`(`quote_order_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_source_type`(`source_type`) USING BTREE,
  INDEX `idx_delivery_type`(`delivery_type`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_member_status_time`(`member_id`, `status`, `create_time`) USING BTREE,
  INDEX `idx_pickup_address_id`(`pickup_address_id`) USING BTREE,
  INDEX `idx_voucher_id`(`voucher_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收订单主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for www_yz_she_recycle_standards
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_recycle_standards`;
CREATE TABLE `www_yz_she_recycle_standards`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标准ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID（关联商品分类）',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标准项标题（如：外观、轻度磨损等）',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '示例图片',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '详细描述',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序权重',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_status_sort`(`category_id`, `status`, `sort`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回收标准表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher`;
CREATE TABLE `www_yz_she_voucher`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '加价券ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `member_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `voucher_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：1-未使用 2-已使用 3-已过期',
  `receive_time` int(11) NOT NULL DEFAULT 0 COMMENT '发放时间',
  `use_time` int(11) NOT NULL DEFAULT 0 COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '使用的订单ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-手动发放',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_voucher_no`(`voucher_no`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_member_id`(`member_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户加价券表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher_send_log
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher_send_log`;
CREATE TABLE `www_yz_she_voucher_send_log`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `template_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `send_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '发放类型：1-注册自动发放 2-全部用户发放 3-指定用户发放',
  `send_count` int(11) NOT NULL DEFAULT 0 COMMENT '发放数量',
  `member_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '指定用户ID，JSON格式',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_template_id`(`template_id`) USING BTREE,
  INDEX `idx_send_type`(`send_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券发放记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for www_yz_she_voucher_template
-- ----------------------------
DROP TABLE IF EXISTS `www_yz_she_voucher_template`;
CREATE TABLE `www_yz_she_voucher_template`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加价券标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '加价券描述',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型：1-固定金额 2-百分比',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额或百分比',
  `min_condition_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最低使用金额',
  `max_discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '最大优惠金额(百分比类型时使用)',
  `sum_count` int(11) NOT NULL DEFAULT -1 COMMENT '发放总数量，-1为不限制',
  `receive_count` int(11) NOT NULL DEFAULT 0 COMMENT '已发放数量',
  `use_count` int(11) NOT NULL DEFAULT 0 COMMENT '已使用数量',
  `valid_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '有效期类型：1-领取后N天有效 2-固定时间段',
  `length` int(11) NOT NULL DEFAULT 30 COMMENT '有效天数',
  `valid_start_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期开始时间',
  `valid_end_time` int(11) NOT NULL DEFAULT 0 COMMENT '有效期结束时间',
  `applicable_goods_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '适用商品类型：1-全部商品 2-指定商品 3-指定分类 4-指定品牌',
  `applicable_goods_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用商品ID，JSON格式',
  `applicable_category_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用分类ID，JSON格式',
  `applicable_brand_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '适用品牌ID，JSON格式',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-未开始 1-进行中 2-已结束 3-已关闭',
  `auto_send_register` tinyint(4) NOT NULL DEFAULT 0 COMMENT '新用户注册自动发放：0-否 1-是',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_time` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '加价券模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Triggers structure for table www_yz_she_quote_orders
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_quote_orders_auto_cancel_time`;
delimiter ;;
CREATE TRIGGER `tr_quote_orders_auto_cancel_time` BEFORE UPDATE ON `www_yz_she_quote_orders` FOR EACH ROW BEGIN
  -- 当状态从待确认(2)变为待发货(3)时，设置48小时后自动取消时间
  IF OLD.status = 2 AND NEW.status = 3 THEN
    SET NEW.auto_cancel_time = DATE_ADD(NOW(), INTERVAL 48 HOUR);
  END IF;

  -- 当状态变为已完成(4)或已取消(5)时，清除自动取消时间
  IF NEW.status IN (4, 5) THEN
    SET NEW.auto_cancel_time = NULL;
  END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table www_yz_she_recycle_orders
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_recycle_orders_status_log`;
delimiter ;;
CREATE TRIGGER `tr_recycle_orders_status_log` AFTER UPDATE ON `www_yz_she_recycle_orders` FOR EACH ROW BEGIN
  -- 当状态发生变化时，自动记录日志
  IF OLD.status != NEW.status THEN
    INSERT INTO `www_yz_she_recycle_order_logs` (
      `recycle_order_id`,
      `from_status`,
      `to_status`,
      `operator_type`,
      `change_reason`,
      `create_time`
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      3, -- 系统操作
      CONCAT('状态从 ', OLD.status, ' 变更为 ', NEW.status),
      UNIX_TIMESTAMP()
    );
  END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
